{"data_mtime": 1757043297, "dep_lines": [1, 1, 1, 1], "dep_prios": [5, 30, 30, 30], "dependencies": ["builtins", "_frozen_importlib", "abc", "typing"], "hash": "da39a3ee5e6b4b0d3255bfef95601890afd80709", "id": "exceptions", "ignore_all": false, "interface_hash": "b9a5ef9d502712718fa0102c51424d2facaac7b2", "mtime": 1752831718, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/impact/pricesmart-promo-v3/backend/exceptions/__init__.py", "plugin_data": null, "size": 0, "suppressed": [], "version_id": "1.17.1"}