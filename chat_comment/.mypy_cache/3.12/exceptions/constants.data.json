{".class": "MypyFile", "_fullname": "exceptions.constants", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BAD_REQUEST_MSG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "exceptions.constants.BAD_REQUEST_MSG", "name": "BAD_REQUEST_MSG", "setter_type": null, "type": "builtins.str"}}, "COMMON_ERROR_MSG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "exceptions.constants.COMMON_ERROR_MSG", "name": "COMMON_ERROR_MSG", "setter_type": null, "type": "builtins.str"}}, "CONFLICT_MSG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "exceptions.constants.CONFLICT_MSG", "name": "CONFLICT_MSG", "setter_type": null, "type": "builtins.str"}}, "EMPTY_EXCLUSION_UPLOAD_MSG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "exceptions.constants.EMPTY_EXCLUSION_UPLOAD_MSG", "name": "EMPTY_EXCLUSION_UPLOAD_MSG", "setter_type": null, "type": "builtins.str"}}, "INVALID_ACCESS_MSG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "exceptions.constants.INVALID_ACCESS_MSG", "name": "INVALID_ACCESS_MSG", "setter_type": null, "type": "builtins.str"}}, "INVALID_TEMPLATE_MSG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "exceptions.constants.INVALID_TEMPLATE_MSG", "name": "INVALID_TEMPLATE_MSG", "setter_type": null, "type": "builtins.str"}}, "NOT_FOUND_MSG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "exceptions.constants.NOT_FOUND_MSG", "name": "NOT_FOUND_MSG", "setter_type": null, "type": "builtins.str"}}, "NO_FILE_NAME_MSG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "exceptions.constants.NO_FILE_NAME_MSG", "name": "NO_FILE_NAME_MSG", "setter_type": null, "type": "builtins.str"}}, "NO_SKU_COLUMN_MSG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "exceptions.constants.NO_SKU_COLUMN_MSG", "name": "NO_SKU_COLUMN_MSG", "setter_type": null, "type": "builtins.str"}}, "NO_SKU_MAPPING_MSG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "exceptions.constants.NO_SKU_MAPPING_MSG", "name": "NO_SKU_MAPPING_MSG", "setter_type": null, "type": "builtins.str"}}, "NO_STORE_COLUMN_MSG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "exceptions.constants.NO_STORE_COLUMN_MSG", "name": "NO_STORE_COLUMN_MSG", "setter_type": null, "type": "builtins.str"}}, "NO_STORE_MAPPING_MSG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "exceptions.constants.NO_STORE_MAPPING_MSG", "name": "NO_STORE_MAPPING_MSG", "setter_type": null, "type": "builtins.str"}}, "OPTIMISATION_ENDPOINT_MSG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "exceptions.constants.OPTIMISATION_ENDPOINT_MSG", "name": "OPTIMISATION_ENDPOINT_MSG", "setter_type": null, "type": "builtins.str"}}, "TIMEOUT_MSG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "exceptions.constants.TIMEOUT_MSG", "name": "TIMEOUT_MSG", "setter_type": null, "type": "builtins.str"}}, "UNDER_MAINTENANCE_MSG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "exceptions.constants.UNDER_MAINTENANCE_MSG", "name": "UNDER_MAINTENANCE_MSG", "setter_type": null, "type": "builtins.str"}}, "UNIQUE_KEY_CONSTRAINT_MSG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "exceptions.constants.UNIQUE_KEY_CONSTRAINT_MSG", "name": "UNIQUE_KEY_CONSTRAINT_MSG", "setter_type": null, "type": "builtins.str"}}, "UNKNOWN_FILE_MSG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "exceptions.constants.UNKNOWN_FILE_MSG", "name": "UNKNOWN_FILE_MSG", "setter_type": null, "type": "builtins.str"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "exceptions.constants.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "exceptions.constants.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "exceptions.constants.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "exceptions.constants.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "exceptions.constants.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "exceptions.constants.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/Users/<USER>/impact/pricesmart-promo-v3/backend/exceptions/constants.py"}