{".class": "MypyFile", "_fullname": "httpcore._exceptions", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ConnectError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpcore._exceptions.NetworkError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpcore._exceptions.ConnectError", "name": "ConnectError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "httpcore._exceptions.ConnectError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpcore._exceptions", "mro": ["httpcore._exceptions.ConnectError", "httpcore._exceptions.NetworkError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpcore._exceptions.ConnectError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpcore._exceptions.ConnectError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConnectTimeout": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpcore._exceptions.TimeoutException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpcore._exceptions.ConnectTimeout", "name": "ConnectTimeout", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "httpcore._exceptions.ConnectTimeout", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpcore._exceptions", "mro": ["httpcore._exceptions.ConnectTimeout", "httpcore._exceptions.TimeoutException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpcore._exceptions.ConnectTimeout.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpcore._exceptions.ConnectTimeout", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConnectionNotAvailable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpcore._exceptions.ConnectionNotAvailable", "name": "ConnectionNotAvailable", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "httpcore._exceptions.ConnectionNotAvailable", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpcore._exceptions", "mro": ["httpcore._exceptions.ConnectionNotAvailable", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpcore._exceptions.ConnectionNotAvailable.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpcore._exceptions.ConnectionNotAvailable", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExceptionMapping": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "httpcore._exceptions.ExceptionMapping", "line": 4, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TypeType", "item": "builtins.Exception"}, {".class": "TypeType", "item": "builtins.Exception"}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "LocalProtocolError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpcore._exceptions.ProtocolError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpcore._exceptions.LocalProtocolError", "name": "LocalProtocolError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "httpcore._exceptions.LocalProtocolError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpcore._exceptions", "mro": ["httpcore._exceptions.LocalProtocolError", "httpcore._exceptions.ProtocolError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpcore._exceptions.LocalProtocolError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpcore._exceptions.LocalProtocolError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "NetworkError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpcore._exceptions.NetworkError", "name": "NetworkError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "httpcore._exceptions.NetworkError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpcore._exceptions", "mro": ["httpcore._exceptions.NetworkError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpcore._exceptions.NetworkError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpcore._exceptions.NetworkError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PoolTimeout": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpcore._exceptions.TimeoutException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpcore._exceptions.PoolTimeout", "name": "PoolTimeout", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "httpcore._exceptions.PoolTimeout", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpcore._exceptions", "mro": ["httpcore._exceptions.PoolTimeout", "httpcore._exceptions.TimeoutException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpcore._exceptions.PoolTimeout.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpcore._exceptions.PoolTimeout", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProtocolError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpcore._exceptions.ProtocolError", "name": "ProtocolError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "httpcore._exceptions.ProtocolError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpcore._exceptions", "mro": ["httpcore._exceptions.ProtocolError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpcore._exceptions.ProtocolError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpcore._exceptions.ProtocolError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProxyError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpcore._exceptions.ProxyError", "name": "ProxyError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "httpcore._exceptions.ProxyError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpcore._exceptions", "mro": ["httpcore._exceptions.ProxyError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpcore._exceptions.ProxyError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpcore._exceptions.ProxyError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ReadError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpcore._exceptions.NetworkError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpcore._exceptions.ReadError", "name": "ReadError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "httpcore._exceptions.ReadError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpcore._exceptions", "mro": ["httpcore._exceptions.ReadError", "httpcore._exceptions.NetworkError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpcore._exceptions.ReadError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpcore._exceptions.ReadError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ReadTimeout": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpcore._exceptions.TimeoutException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpcore._exceptions.ReadTimeout", "name": "ReadTimeout", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "httpcore._exceptions.ReadTimeout", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpcore._exceptions", "mro": ["httpcore._exceptions.ReadTimeout", "httpcore._exceptions.TimeoutException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpcore._exceptions.ReadTimeout.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpcore._exceptions.ReadTimeout", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RemoteProtocolError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpcore._exceptions.ProtocolError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpcore._exceptions.RemoteProtocolError", "name": "RemoteProtocolError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "httpcore._exceptions.RemoteProtocolError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpcore._exceptions", "mro": ["httpcore._exceptions.RemoteProtocolError", "httpcore._exceptions.ProtocolError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpcore._exceptions.RemoteProtocolError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpcore._exceptions.RemoteProtocolError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimeoutException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpcore._exceptions.TimeoutException", "name": "TimeoutException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "httpcore._exceptions.TimeoutException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpcore._exceptions", "mro": ["httpcore._exceptions.TimeoutException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpcore._exceptions.TimeoutException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpcore._exceptions.TimeoutException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "UnsupportedProtocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpcore._exceptions.UnsupportedProtocol", "name": "UnsupportedProtocol", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "httpcore._exceptions.UnsupportedProtocol", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpcore._exceptions", "mro": ["httpcore._exceptions.UnsupportedProtocol", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpcore._exceptions.UnsupportedProtocol.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpcore._exceptions.UnsupportedProtocol", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WriteError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpcore._exceptions.NetworkError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpcore._exceptions.WriteError", "name": "WriteError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "httpcore._exceptions.WriteError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpcore._exceptions", "mro": ["httpcore._exceptions.WriteError", "httpcore._exceptions.NetworkError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpcore._exceptions.WriteError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpcore._exceptions.WriteError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WriteTimeout": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpcore._exceptions.TimeoutException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpcore._exceptions.WriteTimeout", "name": "WriteTimeout", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "httpcore._exceptions.WriteTimeout", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpcore._exceptions", "mro": ["httpcore._exceptions.WriteTimeout", "httpcore._exceptions.TimeoutException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpcore._exceptions.WriteTimeout.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpcore._exceptions.WriteTimeout", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._exceptions.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._exceptions.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._exceptions.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._exceptions.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._exceptions.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._exceptions.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef"}, "map_exceptions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["map"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "httpcore._exceptions.map_exceptions", "name": "map_exceptions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["map"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "httpcore._exceptions.ExceptionMapping"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "map_exceptions", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "httpcore._exceptions.map_exceptions", "name": "map_exceptions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["map"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "httpcore._exceptions.ExceptionMapping"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "map_exceptions", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "path": "/Users/<USER>/.pyenv/versions/3.12/lib/python3.12/site-packages/httpcore/_exceptions.py"}