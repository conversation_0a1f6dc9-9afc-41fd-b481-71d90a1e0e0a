{"data_mtime": 1757043297, "dep_lines": [4, 1, 2, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 30, 30], "dependencies": ["httpcore._models", "contextlib", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "5af1ed744469379f07edf254f4f8bdff8431fe11", "id": "httpcore._sync.interfaces", "ignore_all": true, "interface_hash": "21c7edb967dec57fbc2ed32c0a107b19f498ebc3", "mtime": 1752847433, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/.pyenv/versions/3.12/lib/python3.12/site-packages/httpcore/_sync/interfaces.py", "plugin_data": null, "size": 4365, "suppressed": [], "version_id": "1.17.1"}