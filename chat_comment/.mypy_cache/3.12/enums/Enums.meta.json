{"data_mtime": 1757043297, "dep_lines": [2, 1, 2, 1, 1, 1, 1], "dep_prios": [10, 5, 20, 5, 30, 30, 30], "dependencies": ["client_configuration.constants", "enum", "client_configuration", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "9fb6cb1abea899e92dca04d1875bb1fe22424fe8", "id": "enums.Enums", "ignore_all": false, "interface_hash": "21ecc17b72ba489ecf1971dece34f6a83a401fb6", "mtime": 1754994864, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/impact/pricesmart-promo-v3/backend/enums/Enums.py", "plugin_data": null, "size": 4367, "suppressed": [], "version_id": "1.17.1"}