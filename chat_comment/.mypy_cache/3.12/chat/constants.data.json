{".class": "MypyFile", "_fullname": "chat.constants", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "APP_CODE_BASESMART": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.constants.APP_CODE_BASESMART", "name": "APP_CODE_BASESMART", "setter_type": null, "type": "builtins.str"}}, "APP_CODE_MARKDOWN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.constants.APP_CODE_MARKDOWN", "name": "APP_CODE_MARKDOWN", "setter_type": null, "type": "builtins.str"}}, "APP_CODE_PROMOSMART": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.constants.APP_CODE_PROMOSMART", "name": "APP_CODE_PROMOSMART", "setter_type": null, "type": "builtins.str"}}, "CHAT_API_TAG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.constants.CHAT_API_TAG", "name": "CHAT_API_TAG", "setter_type": null, "type": "builtins.str"}}, "MEMBERS_ADDED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.constants.MEMBERS_ADDED", "name": "MEMBERS_ADDED", "setter_type": null, "type": "builtins.str"}}, "MEMBERS_REMOVED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.constants.MEMBERS_REMOVED", "name": "MEMBERS_REMOVED", "setter_type": null, "type": "builtins.str"}}, "MESSAGES_CLEARED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.constants.MESSAGES_CLEARED", "name": "MESSAGES_CLEARED", "setter_type": null, "type": "builtins.str"}}, "MESSAGE_CREATED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.constants.MESSAGE_CREATED", "name": "MESSAGE_CREATED", "setter_type": null, "type": "builtins.str"}}, "MESSAGE_DELETED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.constants.MESSAGE_DELETED", "name": "MESSAGE_DELETED", "setter_type": null, "type": "builtins.str"}}, "MESSAGE_NOT_FOUND": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.constants.MESSAGE_NOT_FOUND", "name": "MESSAGE_NOT_FOUND", "setter_type": null, "type": "builtins.str"}}, "MESSAGE_UPDATED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.constants.MESSAGE_UPDATED", "name": "MESSAGE_UPDATED", "setter_type": null, "type": "builtins.str"}}, "TOPIC_CREATED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.constants.TOPIC_CREATED", "name": "TOPIC_CREATED", "setter_type": null, "type": "builtins.str"}}, "TOPIC_DELETED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.constants.TOPIC_DELETED", "name": "TOPIC_DELETED", "setter_type": null, "type": "builtins.str"}}, "TOPIC_NOT_FOUND": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.constants.TOPIC_NOT_FOUND", "name": "TOPIC_NOT_FOUND", "setter_type": null, "type": "builtins.str"}}, "TOPIC_UPDATED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.constants.TOPIC_UPDATED", "name": "TOPIC_UPDATED", "setter_type": null, "type": "builtins.str"}}, "USER_NOT_MEMBER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.constants.USER_NOT_MEMBER", "name": "USER_NOT_MEMBER", "setter_type": null, "type": "builtins.str"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chat.constants.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chat.constants.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chat.constants.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chat.constants.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chat.constants.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chat.constants.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/Users/<USER>/impact/pricesmart-promo-v3/backend/chat/constants.py"}