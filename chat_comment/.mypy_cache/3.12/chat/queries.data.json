{".class": "MypyFile", "_fullname": "chat.queries", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ADD_TOPIC_MEMBERS_WITH_UNREAD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.queries.ADD_TOPIC_MEMBERS_WITH_UNREAD", "name": "ADD_TOPIC_MEMBERS_WITH_UNREAD", "setter_type": null, "type": "builtins.str"}}, "CHECK_USER_IS_MEMBER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.queries.CHECK_USER_IS_MEMBER", "name": "CHECK_USER_IS_MEMBER", "setter_type": null, "type": "builtins.str"}}, "CLEAR_MESSAGES_BY_TOPIC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.queries.CLEAR_MESSAGES_BY_TOPIC", "name": "CLEAR_MESSAGES_BY_TOPIC", "setter_type": null, "type": "builtins.str"}}, "CREATE_MESSAGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.queries.CREATE_MESSAGE", "name": "CREATE_MESSAGE", "setter_type": null, "type": "builtins.str"}}, "CREATE_MULTIPLE_UNREAD_ENTRIES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.queries.CREATE_MULTIPLE_UNREAD_ENTRIES", "name": "CREATE_MULTIPLE_UNREAD_ENTRIES", "setter_type": null, "type": "builtins.str"}}, "CREATE_TOPIC_WITH_MEMBERS_AND_UNREAD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.queries.CREATE_TOPIC_WITH_MEMBERS_AND_UNREAD", "name": "CREATE_TOPIC_WITH_MEMBERS_AND_UNREAD", "setter_type": null, "type": "builtins.str"}}, "DELETE_MESSAGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.queries.DELETE_MESSAGE", "name": "DELETE_MESSAGE", "setter_type": null, "type": "builtins.str"}}, "DELETE_TOPIC_WITH_UNREAD_RESET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.queries.DELETE_TOPIC_WITH_UNREAD_RESET", "name": "DELETE_TOPIC_WITH_UNREAD_RESET", "setter_type": null, "type": "builtins.str"}}, "GET_MESSAGES_BY_TOPIC_PAGINATED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.queries.GET_MESSAGES_BY_TOPIC_PAGINATED", "name": "GET_MESSAGES_BY_TOPIC_PAGINATED", "setter_type": null, "type": "builtins.str"}}, "GET_MESSAGES_COUNT_BY_TOPIC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.queries.GET_MESSAGES_COUNT_BY_TOPIC", "name": "GET_MESSAGES_COUNT_BY_TOPIC", "setter_type": null, "type": "builtins.str"}}, "GET_MESSAGE_BY_ID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.queries.GET_MESSAGE_BY_ID", "name": "GET_MESSAGE_BY_ID", "setter_type": null, "type": "builtins.str"}}, "GET_TOPICS_BY_OBJECTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.queries.GET_TOPICS_BY_OBJECTS", "name": "GET_TOPICS_BY_OBJECTS", "setter_type": null, "type": "builtins.str"}}, "GET_TOPICS_BY_OBJECT_TYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.queries.GET_TOPICS_BY_OBJECT_TYPE", "name": "GET_TOPICS_BY_OBJECT_TYPE", "setter_type": null, "type": "builtins.str"}}, "GET_TOPICS_BY_OBJECT_WITH_MEMBERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.queries.GET_TOPICS_BY_OBJECT_WITH_MEMBERS", "name": "GET_TOPICS_BY_OBJECT_WITH_MEMBERS", "setter_type": null, "type": "builtins.str"}}, "GET_TOPICS_BY_TOPIC_ID_WITH_MEMBERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.queries.GET_TOPICS_BY_TOPIC_ID_WITH_MEMBERS", "name": "GET_TOPICS_BY_TOPIC_ID_WITH_MEMBERS", "setter_type": null, "type": "builtins.str"}}, "GET_TOPIC_BY_ID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.queries.GET_TOPIC_BY_ID", "name": "GET_TOPIC_BY_ID", "setter_type": null, "type": "builtins.str"}}, "GET_TOPIC_MEMBERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.queries.GET_TOPIC_MEMBERS", "name": "GET_TOPIC_MEMBERS", "setter_type": null, "type": "builtins.str"}}, "GET_UNREAD_COUNTS_BY_USER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.queries.GET_UNREAD_COUNTS_BY_USER", "name": "GET_UNREAD_COUNTS_BY_USER", "setter_type": null, "type": "builtins.str"}}, "PIN_TOPIC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.queries.PIN_TOPIC", "name": "PIN_TOPIC", "setter_type": null, "type": "builtins.str"}}, "REMOVE_TOPIC_MEMBERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.queries.REMOVE_TOPIC_MEMBERS", "name": "REMOVE_TOPIC_MEMBERS", "setter_type": null, "type": "builtins.str"}}, "RESET_ALL_UNREAD_COUNTS_FOR_TOPIC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.queries.RESET_ALL_UNREAD_COUNTS_FOR_TOPIC", "name": "RESET_ALL_UNREAD_COUNTS_FOR_TOPIC", "setter_type": null, "type": "builtins.str"}}, "RESET_UNREAD_COUNT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.queries.RESET_UNREAD_COUNT", "name": "RESET_UNREAD_COUNT", "setter_type": null, "type": "builtins.str"}}, "UPDATE_MESSAGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.queries.UPDATE_MESSAGE", "name": "UPDATE_MESSAGE", "setter_type": null, "type": "builtins.str"}}, "UPDATE_TOPIC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.queries.UPDATE_TOPIC", "name": "UPDATE_TOPIC", "setter_type": null, "type": "builtins.str"}}, "UPDATE_UNREAD_COUNTS_FOR_OFFLINE_USERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "chat.queries.UPDATE_UNREAD_COUNTS_FOR_OFFLINE_USERS", "name": "UPDATE_UNREAD_COUNTS_FOR_OFFLINE_USERS", "setter_type": null, "type": "builtins.str"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chat.queries.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chat.queries.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chat.queries.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chat.queries.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chat.queries.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chat.queries.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/Users/<USER>/impact/pricesmart-promo-v3/backend/chat/queries.py"}