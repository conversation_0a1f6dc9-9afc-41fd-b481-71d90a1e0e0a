{".class": "MypyFile", "_fullname": "anyio._core._exceptions", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BrokenResourceError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio._core._exceptions.BrokenResourceError", "name": "BrokenResourceError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "anyio._core._exceptions.BrokenResourceError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "anyio._core._exceptions", "mro": ["anyio._core._exceptions.BrokenResourceError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._exceptions.BrokenResourceError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "anyio._core._exceptions.BrokenResourceError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BrokenWorkerProcess": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio._core._exceptions.BrokenWorkerProcess", "name": "BrokenWorkerProcess", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "anyio._core._exceptions.BrokenWorkerProcess", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "anyio._core._exceptions", "mro": ["anyio._core._exceptions.BrokenWorkerProcess", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._exceptions.BrokenWorkerProcess.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "anyio._core._exceptions.BrokenWorkerProcess", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BusyResourceError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio._core._exceptions.BusyResourceError", "name": "BusyResourceError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "anyio._core._exceptions.BusyResourceError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "anyio._core._exceptions", "mro": ["anyio._core._exceptions.BusyResourceError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "action"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "anyio._core._exceptions.BusyResourceError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "action"], "arg_types": ["anyio._core._exceptions.BusyResourceError", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of BusyResourceError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._exceptions.BusyResourceError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "anyio._core._exceptions.BusyResourceError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClosedResourceError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio._core._exceptions.ClosedResourceError", "name": "ClosedResourceError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "anyio._core._exceptions.ClosedResourceError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "anyio._core._exceptions", "mro": ["anyio._core._exceptions.ClosedResourceError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._exceptions.ClosedResourceError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "anyio._core._exceptions.ClosedResourceError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DelimiterNotFound": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio._core._exceptions.DelimiterNotFound", "name": "DelimiterNotFound", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "anyio._core._exceptions.DelimiterNotFound", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "anyio._core._exceptions", "mro": ["anyio._core._exceptions.DelimiterNotFound", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "max_bytes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "anyio._core._exceptions.DelimiterNotFound.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "max_bytes"], "arg_types": ["anyio._core._exceptions.DelimiterNotFound", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of DelimiterNotFound", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._exceptions.DelimiterNotFound.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "anyio._core._exceptions.DelimiterNotFound", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EndOfStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio._core._exceptions.EndOfStream", "name": "EndOfStream", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "anyio._core._exceptions.EndOfStream", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "anyio._core._exceptions", "mro": ["anyio._core._exceptions.EndOfStream", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._exceptions.EndOfStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "anyio._core._exceptions.EndOfStream", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IncompleteRead": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio._core._exceptions.IncompleteRead", "name": "IncompleteRead", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "anyio._core._exceptions.IncompleteRead", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "anyio._core._exceptions", "mro": ["anyio._core._exceptions.IncompleteRead", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "anyio._core._exceptions.IncompleteRead.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["anyio._core._exceptions.IncompleteRead"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of IncompleteRead", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._exceptions.IncompleteRead.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "anyio._core._exceptions.IncompleteRead", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypedAttributeLookupError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.LookupError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio._core._exceptions.TypedAttributeLookupError", "name": "TypedAttributeLookupError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "anyio._core._exceptions.TypedAttributeLookupError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "anyio._core._exceptions", "mro": ["anyio._core._exceptions.TypedAttributeLookupError", "builtins.LookupError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._exceptions.TypedAttributeLookupError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "anyio._core._exceptions.TypedAttributeLookupError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WouldBlock": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio._core._exceptions.WouldBlock", "name": "Would<PERSON>lock", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "anyio._core._exceptions.WouldBlock", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "anyio._core._exceptions", "mro": ["anyio._core._exceptions.WouldBlock", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._exceptions.WouldBlock.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "anyio._core._exceptions.WouldBlock", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio._core._exceptions.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio._core._exceptions.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio._core._exceptions.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio._core._exceptions.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio._core._exceptions.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio._core._exceptions.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}}, "path": "/Users/<USER>/.pyenv/versions/3.12/lib/python3.12/site-packages/anyio/_core/_exceptions.py"}