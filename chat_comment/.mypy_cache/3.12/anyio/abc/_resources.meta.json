{"data_mtime": 1757043297, "dep_lines": [1, 3, 4, 5, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 30], "dependencies": ["__future__", "abc", "types", "typing", "builtins", "_frozen_importlib"], "hash": "0628f2618034526e316c518855d644090ece8d81", "id": "anyio.abc._resources", "ignore_all": true, "interface_hash": "18b1a3d1261e35efca5922c4b82e6843a2bac608", "mtime": 1754466725, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/.pyenv/versions/3.12/lib/python3.12/site-packages/anyio/abc/_resources.py", "plugin_data": null, "size": 783, "suppressed": [], "version_id": "1.17.1"}