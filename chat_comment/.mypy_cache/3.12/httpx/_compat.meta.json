{"data_mtime": 1757043297, "dep_lines": [6, 7, 8, 9, 10, 1, 1, 1, 1, 1, 1, 1, 19, 16, 26], "dep_prios": [10, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 10, 10, 10], "dependencies": ["re", "ssl", "sys", "types", "typing", "builtins", "_collections_abc", "_frozen_importlib", "_ssl", "_typeshed", "abc", "enum"], "hash": "43a087a5befbc23a8ecbaee4f4b4bd836a3c2973", "id": "httpx._compat", "ignore_all": true, "interface_hash": "e07d20e04f01c92bca8d1bb92098f34af3c7a4f9", "mtime": 1753080530, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/.pyenv/versions/3.12/lib/python3.12/site-packages/httpx/_compat.py", "plugin_data": null, "size": 2258, "suppressed": ["brotli", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zstandard"], "version_id": "1.17.1"}