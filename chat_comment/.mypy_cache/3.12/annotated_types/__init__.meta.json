{"data_mtime": 1757043297, "dep_lines": [1, 2, 3, 4, 5, 6, 409, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["math", "sys", "types", "dataclasses", "datetime", "typing", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "abc"], "hash": "4443e6f53aef1f796318cf1df35808e8b1ad92ab", "id": "annotated_types", "ignore_all": true, "interface_hash": "b85c4075eabe693b961d22408fe7913c10847d2e", "mtime": 1753080524, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/.pyenv/versions/3.12/lib/python3.12/site-packages/annotated_types/__init__.py", "plugin_data": null, "size": 13819, "suppressed": [], "version_id": "1.17.1"}