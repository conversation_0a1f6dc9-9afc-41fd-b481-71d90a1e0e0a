{"data_mtime": 1757043296, "dep_lines": [1, 2, 3, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 30, 30, 30], "dependencies": ["re", "abc", "typing", "builtins", "_frozen_importlib", "enum", "types"], "hash": "364b07ac4632973cfa38a3fb89afca626c45836c", "id": "dotenv.variables", "ignore_all": true, "interface_hash": "0736201a8d30a27230ce098104ca3bf52e9e4d56", "mtime": 1752847427, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/.pyenv/versions/3.12/lib/python3.12/site-packages/dotenv/variables.py", "plugin_data": null, "size": 2348, "suppressed": [], "version_id": "1.17.1"}