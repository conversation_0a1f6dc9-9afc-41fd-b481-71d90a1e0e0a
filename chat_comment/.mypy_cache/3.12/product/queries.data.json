{".class": "MypyFile", "_fullname": "product.queries", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "FETCH_CONFIG_DETAILS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "product.queries.FETCH_CONFIG_DETAILS_QUERY", "name": "FETCH_CONFIG_DETAILS_QUERY", "setter_type": null, "type": "builtins.str"}}, "GET_CUSTOMER_TYPE_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "product.queries.GET_CUSTOMER_TYPE_QUERY", "name": "GET_CUSTOMER_TYPE_QUERY", "setter_type": null, "type": "builtins.str"}}, "GET_H5_DETAILS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "product.queries.GET_H5_DETAILS_QUERY", "name": "GET_H5_DETAILS_QUERY", "setter_type": null, "type": "builtins.str"}}, "GET_HIERARCHY_FILTERS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "product.queries.GET_HIERARCHY_FILTERS_QUERY", "name": "GET_HIERARCHY_FILTERS_QUERY", "setter_type": null, "type": "builtins.str"}}, "GET_OFFER_DISTRIBUTION_CHANNEL_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "product.queries.GET_OFFER_DISTRIBUTION_CHANNEL_QUERY", "name": "GET_OFFER_DISTRIBUTION_CHANNEL_QUERY", "setter_type": null, "type": "builtins.str"}}, "GET_OFFER_TYPE_DETAILS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "product.queries.GET_OFFER_TYPE_DETAILS_QUERY", "name": "GET_OFFER_TYPE_DETAILS_QUERY", "setter_type": null, "type": "builtins.str"}}, "GET_PRODUCTS_DETAILS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "product.queries.GET_PRODUCTS_DETAILS_QUERY", "name": "GET_PRODUCTS_DETAILS_QUERY", "setter_type": null, "type": "builtins.str"}}, "GET_STORES_DETAILS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "product.queries.GET_STORES_DETAILS_QUERY", "name": "GET_STORES_DETAILS_QUERY", "setter_type": null, "type": "builtins.str"}}, "GET_VALID_PRODUCT_IDS_EVENT_BASED_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "product.queries.GET_VALID_PRODUCT_IDS_EVENT_BASED_QUERY", "name": "GET_VALID_PRODUCT_IDS_EVENT_BASED_QUERY", "setter_type": null, "type": "builtins.str"}}, "GET_VALID_STORE_DETAILS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "product.queries.GET_VALID_STORE_DETAILS_QUERY", "name": "GET_VALID_STORE_DETAILS_QUERY", "setter_type": null, "type": "builtins.str"}}, "GET_VALID_STORE_IDS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "product.queries.GET_VALID_STORE_IDS_QUERY", "name": "GET_VALID_STORE_IDS_QUERY", "setter_type": null, "type": "builtins.str"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "product.queries.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "product.queries.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "product.queries.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "product.queries.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "product.queries.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "product.queries.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/Users/<USER>/impact/pricesmart-promo-v3/backend/product/queries.py"}