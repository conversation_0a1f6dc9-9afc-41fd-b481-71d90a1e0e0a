{"data_mtime": 1757043297, "dep_lines": [5, 1, 1, 1, 1], "dep_prios": [5, 5, 30, 30, 30], "dependencies": ["__future__", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "a98c86bcf7ca52aab41be385fdd7b0aa6130c454", "id": "cryptography.hazmat.decrepit.ciphers", "ignore_all": true, "interface_hash": "db72876dab4a94a3f7f80e3f964ddb95bbcee839", "mtime": 1753080526, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/.pyenv/versions/3.12/lib/python3.12/site-packages/cryptography/hazmat/decrepit/ciphers/__init__.py", "plugin_data": null, "size": 216, "suppressed": [], "version_id": "1.17.1"}