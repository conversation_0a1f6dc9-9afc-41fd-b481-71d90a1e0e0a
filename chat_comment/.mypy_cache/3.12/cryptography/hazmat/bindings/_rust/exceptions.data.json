{".class": "MypyFile", "_fullname": "cryptography.hazmat.bindings._rust.exceptions", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "_Reasons": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.bindings._rust.exceptions._Reasons", "name": "_Reasons", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.exceptions._Reasons", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat.bindings._rust.exceptions", "mro": ["cryptography.hazmat.bindings._rust.exceptions._Reasons", "builtins.object"], "names": {".class": "SymbolTable", "BACKEND_MISSING_INTERFACE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cryptography.hazmat.bindings._rust.exceptions._Reasons.BACKEND_MISSING_INTERFACE", "name": "BACKEND_MISSING_INTERFACE", "setter_type": null, "type": "cryptography.hazmat.bindings._rust.exceptions._Reasons"}}, "UNSUPPORTED_CIPHER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cryptography.hazmat.bindings._rust.exceptions._Reasons.UNSUPPORTED_CIPHER", "name": "UNSUPPORTED_CIPHER", "setter_type": null, "type": "cryptography.hazmat.bindings._rust.exceptions._Reasons"}}, "UNSUPPORTED_DIFFIE_HELLMAN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cryptography.hazmat.bindings._rust.exceptions._Reasons.UNSUPPORTED_DIFFIE_HELLMAN", "name": "UNSUPPORTED_DIFFIE_HELLMAN", "setter_type": null, "type": "cryptography.hazmat.bindings._rust.exceptions._Reasons"}}, "UNSUPPORTED_ELLIPTIC_CURVE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cryptography.hazmat.bindings._rust.exceptions._Reasons.UNSUPPORTED_ELLIPTIC_CURVE", "name": "UNSUPPORTED_ELLIPTIC_CURVE", "setter_type": null, "type": "cryptography.hazmat.bindings._rust.exceptions._Reasons"}}, "UNSUPPORTED_EXCHANGE_ALGORITHM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cryptography.hazmat.bindings._rust.exceptions._Reasons.UNSUPPORTED_EXCHANGE_ALGORITHM", "name": "UNSUPPORTED_EXCHANGE_ALGORITHM", "setter_type": null, "type": "cryptography.hazmat.bindings._rust.exceptions._Reasons"}}, "UNSUPPORTED_HASH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cryptography.hazmat.bindings._rust.exceptions._Reasons.UNSUPPORTED_HASH", "name": "UNSUPPORTED_HASH", "setter_type": null, "type": "cryptography.hazmat.bindings._rust.exceptions._Reasons"}}, "UNSUPPORTED_MAC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cryptography.hazmat.bindings._rust.exceptions._Reasons.UNSUPPORTED_MAC", "name": "UNSUPPORTED_MAC", "setter_type": null, "type": "cryptography.hazmat.bindings._rust.exceptions._Reasons"}}, "UNSUPPORTED_MGF": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cryptography.hazmat.bindings._rust.exceptions._Reasons.UNSUPPORTED_MGF", "name": "UNSUPPORTED_MGF", "setter_type": null, "type": "cryptography.hazmat.bindings._rust.exceptions._Reasons"}}, "UNSUPPORTED_PADDING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cryptography.hazmat.bindings._rust.exceptions._Reasons.UNSUPPORTED_PADDING", "name": "UNSUPPORTED_PADDING", "setter_type": null, "type": "cryptography.hazmat.bindings._rust.exceptions._Reasons"}}, "UNSUPPORTED_PUBLIC_KEY_ALGORITHM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cryptography.hazmat.bindings._rust.exceptions._Reasons.UNSUPPORTED_PUBLIC_KEY_ALGORITHM", "name": "UNSUPPORTED_PUBLIC_KEY_ALGORITHM", "setter_type": null, "type": "cryptography.hazmat.bindings._rust.exceptions._Reasons"}}, "UNSUPPORTED_SERIALIZATION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cryptography.hazmat.bindings._rust.exceptions._Reasons.UNSUPPORTED_SERIALIZATION", "name": "UNSUPPORTED_SERIALIZATION", "setter_type": null, "type": "cryptography.hazmat.bindings._rust.exceptions._Reasons"}}, "UNSUPPORTED_X509": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cryptography.hazmat.bindings._rust.exceptions._Reasons.UNSUPPORTED_X509", "name": "UNSUPPORTED_X509", "setter_type": null, "type": "cryptography.hazmat.bindings._rust.exceptions._Reasons"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.bindings._rust.exceptions._Reasons.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.bindings._rust.exceptions._Reasons", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.exceptions.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.exceptions.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.exceptions.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.exceptions.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.exceptions.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.exceptions.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/Users/<USER>/.pyenv/versions/3.12/lib/python3.12/site-packages/cryptography/hazmat/bindings/_rust/exceptions.pyi"}