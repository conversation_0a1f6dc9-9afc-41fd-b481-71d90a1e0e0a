{".class": "MypyFile", "_fullname": "filters.constants", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BRAND": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.BRAND", "name": "BRAND", "setter_type": null, "type": "builtins.str"}}, "CHANNEL_BNM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.CHANNEL_BNM", "name": "CHANNEL_BNM", "setter_type": null, "type": "builtins.str"}}, "CHANNEL_ECOMMERCE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.CHANNEL_ECOMMERCE", "name": "CHANNEL_ECOMMERCE", "setter_type": null, "type": "builtins.str"}}, "CHANNEL_VALUES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "filters.constants.CHANNEL_VALUES", "name": "CHANNEL_VALUES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "COLOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.COLOR", "name": "COLOR", "setter_type": null, "type": "builtins.str"}}, "HIERARCHY_ID_MAPPING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "filters.constants.HIERARCHY_ID_MAPPING", "name": "HIERARCHY_ID_MAPPING", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.object", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "HIERARCHY_NAME_MAPPING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "filters.constants.HIERARCHY_NAME_MAPPING", "name": "HIERARCHY_NAME_MAPPING", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.object", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "HIERARCHY_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "filters.constants.HIERARCHY_TYPES", "name": "HIERARCHY_TYPES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "LIFECYCLE_INDICATOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.LIFECYCLE_INDICATOR", "name": "LIFECYCLE_INDICATOR", "setter_type": null, "type": "builtins.str"}}, "NUM_BRAND": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.NUM_BRAND", "name": "NUM_BRAND", "setter_type": null, "type": "builtins.int"}}, "NUM_COLOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.NUM_COLOR", "name": "NUM_COLOR", "setter_type": null, "type": "builtins.int"}}, "NUM_PROD_HIERARCHY0": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.NUM_PROD_HIERARCHY0", "name": "NUM_PROD_HIERARCHY0", "setter_type": null, "type": "builtins.int"}}, "NUM_PROD_HIERARCHY1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.NUM_PROD_HIERARCHY1", "name": "NUM_PROD_HIERARCHY1", "setter_type": null, "type": "builtins.int"}}, "NUM_PROD_HIERARCHY2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.NUM_PROD_HIERARCHY2", "name": "NUM_PROD_HIERARCHY2", "setter_type": null, "type": "builtins.int"}}, "NUM_PROD_HIERARCHY3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.NUM_PROD_HIERARCHY3", "name": "NUM_PROD_HIERARCHY3", "setter_type": null, "type": "builtins.int"}}, "NUM_PROD_HIERARCHY4": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.NUM_PROD_HIERARCHY4", "name": "NUM_PROD_HIERARCHY4", "setter_type": null, "type": "builtins.int"}}, "NUM_PROD_HIERARCHY5": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.NUM_PROD_HIERARCHY5", "name": "NUM_PROD_HIERARCHY5", "setter_type": null, "type": "builtins.int"}}, "NUM_STORE_HIERARCHY0": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.NUM_STORE_HIERARCHY0", "name": "NUM_STORE_HIERARCHY0", "setter_type": null, "type": "builtins.int"}}, "NUM_STORE_HIERARCHY1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.NUM_STORE_HIERARCHY1", "name": "NUM_STORE_HIERARCHY1", "setter_type": null, "type": "builtins.int"}}, "NUM_STORE_HIERARCHY2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.NUM_STORE_HIERARCHY2", "name": "NUM_STORE_HIERARCHY2", "setter_type": null, "type": "builtins.int"}}, "NUM_STORE_HIERARCHY3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.NUM_STORE_HIERARCHY3", "name": "NUM_STORE_HIERARCHY3", "setter_type": null, "type": "builtins.int"}}, "NUM_STORE_HIERARCHY4": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.NUM_STORE_HIERARCHY4", "name": "NUM_STORE_HIERARCHY4", "setter_type": null, "type": "builtins.int"}}, "NUM_STORE_HIERARCHY5": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.NUM_STORE_HIERARCHY5", "name": "NUM_STORE_HIERARCHY5", "setter_type": null, "type": "builtins.int"}}, "NUM_STORE_HIERARCHY6": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.NUM_STORE_HIERARCHY6", "name": "NUM_STORE_HIERARCHY6", "setter_type": null, "type": "builtins.int"}}, "NUM_STYLE_COLOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.NUM_STYLE_COLOR", "name": "NUM_STYLE_COLOR", "setter_type": null, "type": "builtins.int"}}, "PH_ID_MAPPING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "filters.constants.PH_ID_MAPPING", "name": "PH_ID_MAPPING", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.object", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "PH_NAME_MAPPING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "filters.constants.PH_NAME_MAPPING", "name": "PH_NAME_MAPPING", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.object", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "PRODUCT_HIERARCHIES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "filters.constants.PRODUCT_HIERARCHIES", "name": "PRODUCT_HIERARCHIES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "PRODUCT_HIERARCHY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.PRODUCT_HIERARCHY", "name": "PRODUCT_HIERARCHY", "setter_type": null, "type": "builtins.str"}}, "PRODUCT_TABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.PRODUCT_TABLE", "name": "PRODUCT_TABLE", "setter_type": null, "type": "builtins.str"}}, "PRODUCT_TABLE_ALIAS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.PRODUCT_TABLE_ALIAS", "name": "PRODUCT_TABLE_ALIAS", "setter_type": null, "type": "builtins.str"}}, "PROD_HIERARCHY0": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.PROD_HIERARCHY0", "name": "PROD_HIERARCHY0", "setter_type": null, "type": "builtins.str"}}, "PROD_HIERARCHY1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.PROD_HIERARCHY1", "name": "PROD_HIERARCHY1", "setter_type": null, "type": "builtins.str"}}, "PROD_HIERARCHY2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.PROD_HIERARCHY2", "name": "PROD_HIERARCHY2", "setter_type": null, "type": "builtins.str"}}, "PROD_HIERARCHY3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.PROD_HIERARCHY3", "name": "PROD_HIERARCHY3", "setter_type": null, "type": "builtins.str"}}, "PROD_HIERARCHY4": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.PROD_HIERARCHY4", "name": "PROD_HIERARCHY4", "setter_type": null, "type": "builtins.str"}}, "PROD_HIERARCHY5": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.PROD_HIERARCHY5", "name": "PROD_HIERARCHY5", "setter_type": null, "type": "builtins.str"}}, "SH_ID_MAPPING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "filters.constants.SH_ID_MAPPING", "name": "SH_ID_MAPPING", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.object", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "SH_NAME_MAPPING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "filters.constants.SH_NAME_MAPPING", "name": "SH_NAME_MAPPING", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.object", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "STORE_HIERARCHIES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "filters.constants.STORE_HIERARCHIES", "name": "STORE_HIERARCHIES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "STORE_HIERARCHY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.STORE_HIERARCHY", "name": "STORE_HIERARCHY", "setter_type": null, "type": "builtins.str"}}, "STORE_HIERARCHY0": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.STORE_HIERARCHY0", "name": "STORE_HIERARCHY0", "setter_type": null, "type": "builtins.str"}}, "STORE_HIERARCHY1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.STORE_HIERARCHY1", "name": "STORE_HIERARCHY1", "setter_type": null, "type": "builtins.str"}}, "STORE_HIERARCHY2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.STORE_HIERARCHY2", "name": "STORE_HIERARCHY2", "setter_type": null, "type": "builtins.str"}}, "STORE_HIERARCHY3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.STORE_HIERARCHY3", "name": "STORE_HIERARCHY3", "setter_type": null, "type": "builtins.str"}}, "STORE_HIERARCHY4": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.STORE_HIERARCHY4", "name": "STORE_HIERARCHY4", "setter_type": null, "type": "builtins.str"}}, "STORE_HIERARCHY5": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.STORE_HIERARCHY5", "name": "STORE_HIERARCHY5", "setter_type": null, "type": "builtins.str"}}, "STORE_HIERARCHY6": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.STORE_HIERARCHY6", "name": "STORE_HIERARCHY6", "setter_type": null, "type": "builtins.str"}}, "STORE_TABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.STORE_TABLE", "name": "STORE_TABLE", "setter_type": null, "type": "builtins.str"}}, "STORE_TABLE_ALIAS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.STORE_TABLE_ALIAS", "name": "STORE_TABLE_ALIAS", "setter_type": null, "type": "builtins.str"}}, "STR_NUM_BRAND": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.STR_NUM_BRAND", "name": "STR_NUM_BRAND", "setter_type": null, "type": "builtins.str"}}, "STR_NUM_COLOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.STR_NUM_COLOR", "name": "STR_NUM_COLOR", "setter_type": null, "type": "builtins.str"}}, "STR_NUM_PROD_HIERARCHY0": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.STR_NUM_PROD_HIERARCHY0", "name": "STR_NUM_PROD_HIERARCHY0", "setter_type": null, "type": "builtins.str"}}, "STR_NUM_PROD_HIERARCHY1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.STR_NUM_PROD_HIERARCHY1", "name": "STR_NUM_PROD_HIERARCHY1", "setter_type": null, "type": "builtins.str"}}, "STR_NUM_PROD_HIERARCHY2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.STR_NUM_PROD_HIERARCHY2", "name": "STR_NUM_PROD_HIERARCHY2", "setter_type": null, "type": "builtins.str"}}, "STR_NUM_PROD_HIERARCHY3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.STR_NUM_PROD_HIERARCHY3", "name": "STR_NUM_PROD_HIERARCHY3", "setter_type": null, "type": "builtins.str"}}, "STR_NUM_PROD_HIERARCHY4": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.STR_NUM_PROD_HIERARCHY4", "name": "STR_NUM_PROD_HIERARCHY4", "setter_type": null, "type": "builtins.str"}}, "STR_NUM_PROD_HIERARCHY5": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.STR_NUM_PROD_HIERARCHY5", "name": "STR_NUM_PROD_HIERARCHY5", "setter_type": null, "type": "builtins.str"}}, "STR_NUM_STORE_HIERARCHY0": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.STR_NUM_STORE_HIERARCHY0", "name": "STR_NUM_STORE_HIERARCHY0", "setter_type": null, "type": "builtins.str"}}, "STR_NUM_STORE_HIERARCHY1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.STR_NUM_STORE_HIERARCHY1", "name": "STR_NUM_STORE_HIERARCHY1", "setter_type": null, "type": "builtins.str"}}, "STR_NUM_STORE_HIERARCHY2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.STR_NUM_STORE_HIERARCHY2", "name": "STR_NUM_STORE_HIERARCHY2", "setter_type": null, "type": "builtins.str"}}, "STR_NUM_STORE_HIERARCHY3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.STR_NUM_STORE_HIERARCHY3", "name": "STR_NUM_STORE_HIERARCHY3", "setter_type": null, "type": "builtins.str"}}, "STR_NUM_STORE_HIERARCHY4": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.STR_NUM_STORE_HIERARCHY4", "name": "STR_NUM_STORE_HIERARCHY4", "setter_type": null, "type": "builtins.str"}}, "STR_NUM_STORE_HIERARCHY5": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.STR_NUM_STORE_HIERARCHY5", "name": "STR_NUM_STORE_HIERARCHY5", "setter_type": null, "type": "builtins.str"}}, "STR_NUM_STORE_HIERARCHY6": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.STR_NUM_STORE_HIERARCHY6", "name": "STR_NUM_STORE_HIERARCHY6", "setter_type": null, "type": "builtins.str"}}, "STR_NUM_STYLE_COLOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.STR_NUM_STYLE_COLOR", "name": "STR_NUM_STYLE_COLOR", "setter_type": null, "type": "builtins.str"}}, "STYLE_COLOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "filters.constants.STYLE_COLOR", "name": "STYLE_COLOR", "setter_type": null, "type": "builtins.str"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "filters.constants.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "filters.constants.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "filters.constants.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "filters.constants.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "filters.constants.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "filters.constants.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/Users/<USER>/impact/pricesmart-promo-v3/backend/filters/constants.py"}