{".class": "MypyFile", "_fullname": "filters.types", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ExcelConfigInfoType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "filters.types.ExcelConfigInfoType", "name": "ExcelConfigInfoType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "filters.types.ExcelConfigInfoType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "filters.types", "mro": ["filters.types.ExcelConfigInfoType", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["value_column", "builtins.str"], ["order", "builtins.int"]], "readonly_keys": [], "required_keys": ["order", "value_column"]}}}, "ExclusionUploadConfigInfoType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "filters.types.ExclusionUploadConfigInfoType", "name": "ExclusionUploadConfigInfoType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "filters.types.ExclusionUploadConfigInfoType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "filters.types", "mro": ["filters.types.ExclusionUploadConfigInfoType", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["value_column", "builtins.str"], ["order", "builtins.int"]], "readonly_keys": [], "required_keys": ["order", "value_column"]}}}, "HierarchyFiltersInfoType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "filters.types.HierarchyFiltersInfoType", "name": "HierarchyFiltersInfoType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "filters.types.HierarchyFiltersInfoType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "filters.types", "mro": ["filters.types.HierarchyFiltersInfoType", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["id_column", "builtins.str"], ["value_column", "builtins.str"], ["id", "builtins.int"], ["is_linked_to_event", "builtins.bool"], ["is_linked_to_promo", "builtins.bool"], ["is_linked_to_downloads", "builtins.bool"], ["is_hierarchy", "builtins.bool"], ["label", "builtins.str"]], "readonly_keys": [], "required_keys": ["id", "id_column", "is_hierarchy", "is_linked_to_downloads", "is_linked_to_event", "is_linked_to_promo", "label", "value_column"]}}}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "filters.types.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "filters.types.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "filters.types.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "filters.types.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "filters.types.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "filters.types.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/Users/<USER>/impact/pricesmart-promo-v3/backend/filters/types.py"}