{".class": "MypyFile", "_fullname": "client_configuration.utils", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "IdentifierEnum": {".class": "SymbolTableNode", "cross_ref": "enums.Enums.IdentifierEnum", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "client_configuration.utils.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "client_configuration.utils.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "client_configuration.utils.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "client_configuration.utils.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "client_configuration.utils.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "client_configuration.utils.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "client_configuration_constants": {".class": "SymbolTableNode", "cross_ref": "client_configuration.constants", "kind": "Gdef"}, "update_client_configuration_constants": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["identifiers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "client_configuration.utils.update_client_configuration_constants", "name": "update_client_configuration_constants", "type": null}}, "update_event_identifiers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event_identifiers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "client_configuration.utils.update_event_identifiers", "name": "update_event_identifiers", "type": null}}, "update_promo_identifiers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["promo_identifiers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "client_configuration.utils.update_promo_identifiers", "name": "update_promo_identifiers", "type": null}}}, "path": "/Users/<USER>/impact/pricesmart-promo-v3/backend/client_configuration/utils.py"}