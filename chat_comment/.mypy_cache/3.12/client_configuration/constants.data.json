{".class": "MypyFile", "_fullname": "client_configuration.constants", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "EVENT_IDENTIFIER_ALIAS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "client_configuration.constants.EVENT_IDENTIFIER_ALIAS", "name": "EVENT_IDENTIFIER_ALIAS", "setter_type": null, "type": "builtins.str"}}, "EVENT_IDENTIFIER_ALIAS_PLURAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "client_configuration.constants.EVENT_IDENTIFIER_ALIAS_PLURAL", "name": "EVENT_IDENTIFIER_ALIAS_PLURAL", "setter_type": null, "type": "builtins.str"}}, "EVENT_IDENTIFIER_EXPANDED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "client_configuration.constants.EVENT_IDENTIFIER_EXPANDED", "name": "EVENT_IDENTIFIER_EXPANDED", "setter_type": null, "type": "builtins.str"}}, "EVENT_IDENTIFIER_PLURAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "client_configuration.constants.EVENT_IDENTIFIER_PLURAL", "name": "EVENT_IDENTIFIER_PLURAL", "setter_type": null, "type": "builtins.str"}}, "EVENT_IDENTIFIER_PRIMARY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "client_configuration.constants.EVENT_IDENTIFIER_PRIMARY", "name": "EVENT_IDENTIFIER_PRIMARY", "setter_type": null, "type": "builtins.str"}}, "EVENT_IDENTIFIER_STANDARD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "client_configuration.constants.EVENT_IDENTIFIER_STANDARD", "name": "EVENT_IDENTIFIER_STANDARD", "setter_type": null, "type": "builtins.str"}}, "EVENT_IDENTIFIER_STANDARD_PLURAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "client_configuration.constants.EVENT_IDENTIFIER_STANDARD_PLURAL", "name": "EVENT_IDENTIFIER_STANDARD_PLURAL", "setter_type": null, "type": "builtins.str"}}, "PROMO_IDENTIFIER_ALIAS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "client_configuration.constants.PROMO_IDENTIFIER_ALIAS", "name": "PROMO_IDENTIFIER_ALIAS", "setter_type": null, "type": "builtins.str"}}, "PROMO_IDENTIFIER_ALIAS_PLURAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "client_configuration.constants.PROMO_IDENTIFIER_ALIAS_PLURAL", "name": "PROMO_IDENTIFIER_ALIAS_PLURAL", "setter_type": null, "type": "builtins.str"}}, "PROMO_IDENTIFIER_EXPANDED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "client_configuration.constants.PROMO_IDENTIFIER_EXPANDED", "name": "PROMO_IDENTIFIER_EXPANDED", "setter_type": null, "type": "builtins.str"}}, "PROMO_IDENTIFIER_PLURAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "client_configuration.constants.PROMO_IDENTIFIER_PLURAL", "name": "PROMO_IDENTIFIER_PLURAL", "setter_type": null, "type": "builtins.str"}}, "PROMO_IDENTIFIER_PRIMARY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "client_configuration.constants.PROMO_IDENTIFIER_PRIMARY", "name": "PROMO_IDENTIFIER_PRIMARY", "setter_type": null, "type": "builtins.str"}}, "PROMO_IDENTIFIER_STANDARD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "client_configuration.constants.PROMO_IDENTIFIER_STANDARD", "name": "PROMO_IDENTIFIER_STANDARD", "setter_type": null, "type": "builtins.str"}}, "PROMO_IDENTIFIER_STANDARD_PLURAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "client_configuration.constants.PROMO_IDENTIFIER_STANDARD_PLURAL", "name": "PROMO_IDENTIFIER_STANDARD_PLURAL", "setter_type": null, "type": "builtins.str"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "client_configuration.constants.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "client_configuration.constants.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "client_configuration.constants.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "client_configuration.constants.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "client_configuration.constants.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "client_configuration.constants.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/Users/<USER>/impact/pricesmart-promo-v3/backend/client_configuration/constants.py"}