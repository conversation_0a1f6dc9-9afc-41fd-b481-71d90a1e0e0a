{".class": "MypyFile", "_fullname": "encodings.idna", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Codec": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["codecs.Codec"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "encodings.idna.Codec", "name": "Codec", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "encodings.idna.Codec", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "encodings.idna", "mro": ["encodings.idna.Codec", "codecs.Codec", "builtins.object"], "names": {".class": "SymbolTable", "decode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "input", "errors"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "encodings.idna.Codec.decode", "name": "decode", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "input", "errors"], "arg_types": ["encodings.idna.Codec", {".class": "UnionType", "items": ["_collections_abc.<PERSON><PERSON>er", "builtins.str"], "uses_pep604_syntax": true}, "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "decode of Codec", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "encode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "input", "errors"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "encodings.idna.Codec.encode", "name": "encode", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "input", "errors"], "arg_types": ["encodings.idna.Codec", "builtins.str", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "encode of Codec", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "encodings.idna.Codec.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "encodings.idna.Codec", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IncrementalDecoder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["codecs.BufferedIncrementalDecoder"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "encodings.idna.IncrementalDecoder", "name": "IncrementalDecoder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "encodings.idna.IncrementalDecoder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "encodings.idna", "mro": ["encodings.idna.IncrementalDecoder", "codecs.BufferedIncrementalDecoder", "codecs.IncrementalDecoder", "builtins.object"], "names": {".class": "SymbolTable", "_buffer_decode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "input", "errors", "final"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "encodings.idna.IncrementalDecoder._buffer_decode", "name": "_buffer_decode", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "input", "errors", "final"], "arg_types": ["encodings.idna.IncrementalDecoder", {".class": "UnionType", "items": ["_collections_abc.<PERSON><PERSON>er", "builtins.str"], "uses_pep604_syntax": true}, "builtins.str", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_buffer_decode of IncrementalDecoder", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "encodings.idna.IncrementalDecoder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "encodings.idna.IncrementalDecoder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IncrementalEncoder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["codecs.BufferedIncrementalEncoder"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "encodings.idna.IncrementalEncoder", "name": "IncrementalEncoder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "encodings.idna.IncrementalEncoder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "encodings.idna", "mro": ["encodings.idna.IncrementalEncoder", "codecs.BufferedIncrementalEncoder", "codecs.IncrementalEncoder", "builtins.object"], "names": {".class": "SymbolTable", "_buffer_encode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "input", "errors", "final"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "encodings.idna.IncrementalEncoder._buffer_encode", "name": "_buffer_encode", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "input", "errors", "final"], "arg_types": ["encodings.idna.IncrementalEncoder", "builtins.str", "builtins.str", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_buffer_encode of IncrementalEncoder", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "encodings.idna.IncrementalEncoder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "encodings.idna.IncrementalEncoder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ReadableBuffer": {".class": "SymbolTableNode", "cross_ref": "_typeshed.ReadableBuffer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StreamReader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["encodings.idna.Codec", "codecs.StreamReader"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "encodings.idna.StreamReader", "name": "StreamReader", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "encodings.idna.StreamReader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "encodings.idna", "mro": ["encodings.idna.StreamReader", "encodings.idna.Codec", "codecs.StreamReader", "codecs.Codec", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "encodings.idna.StreamReader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "encodings.idna.StreamReader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StreamWriter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["encodings.idna.Codec", "codecs.StreamWriter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "encodings.idna.StreamWriter", "name": "StreamWriter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "encodings.idna.StreamWriter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "encodings.idna", "mro": ["encodings.idna.StreamWriter", "encodings.idna.Codec", "codecs.StreamWriter", "codecs.Codec", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "encodings.idna.StreamWriter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "encodings.idna.StreamWriter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ToASCII": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["label"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "encodings.idna.ToASCII", "name": "ToASCII", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["label"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "ToASCII", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ToUnicode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["label"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "encodings.idna.ToUnicode", "name": "ToUnicode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["label"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", "builtins.str"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "ToUnicode", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "encodings.idna.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "encodings.idna.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "encodings.idna.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "encodings.idna.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "encodings.idna.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "encodings.idna.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "ace_prefix": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "encodings.idna.ace_prefix", "name": "ace_prefix", "setter_type": null, "type": "builtins.bytes"}}, "codecs": {".class": "SymbolTableNode", "cross_ref": "codecs", "kind": "Gdef", "module_hidden": true, "module_public": false}, "dots": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "encodings.idna.dots", "name": "dots", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "getregentry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "encodings.idna.getregentry", "name": "getregentry", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "getregentry", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "codecs.CodecInfo"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "nameprep": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["label"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "encodings.idna.nameprep", "name": "nameprep", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["label"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "nameprep", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sace_prefix": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "encodings.idna.sace_prefix", "name": "sace_prefix", "setter_type": null, "type": "builtins.str"}}}, "path": "/Users/<USER>/.pyenv/versions/3.12/lib/python3.12/site-packages/mypy/typeshed/stdlib/encodings/idna.pyi"}