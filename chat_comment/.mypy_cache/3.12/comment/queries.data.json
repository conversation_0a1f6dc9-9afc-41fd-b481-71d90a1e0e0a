{".class": "MypyFile", "_fullname": "comment.queries", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CREATE_COMMENT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "comment.queries.CREATE_COMMENT", "name": "CREATE_COMMENT", "setter_type": null, "type": "builtins.str"}}, "CREATE_COMMENT_REPLY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "comment.queries.CREATE_COMMENT_REPLY", "name": "CREATE_COMMENT_REPLY", "setter_type": null, "type": "builtins.str"}}, "DELETE_COMMENT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "comment.queries.DELETE_COMMENT", "name": "DELETE_COMMENT", "setter_type": null, "type": "builtins.str"}}, "DELETE_COMMENT_REPLY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "comment.queries.DELETE_COMMENT_REPLY", "name": "DELETE_COMMENT_REPLY", "setter_type": null, "type": "builtins.str"}}, "GET_COMMENTS_WITH_REPLIES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "comment.queries.GET_COMMENTS_WITH_REPLIES", "name": "GET_COMMENTS_WITH_REPLIES", "setter_type": null, "type": "builtins.str"}}, "GET_COMMENT_BY_ID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "comment.queries.GET_COMMENT_BY_ID", "name": "GET_COMMENT_BY_ID", "setter_type": null, "type": "builtins.str"}}, "GET_COMMENT_HIGHLIGHTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "comment.queries.GET_COMMENT_HIGHLIGHTS", "name": "GET_COMMENT_HIGHLIGHTS", "setter_type": null, "type": "builtins.str"}}, "GET_COMMENT_IDS_BY_USERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "comment.queries.GET_COMMENT_IDS_BY_USERS", "name": "GET_COMMENT_IDS_BY_USERS", "setter_type": null, "type": "builtins.str"}}, "GET_COMMENT_REPLY_BY_ID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "comment.queries.GET_COMMENT_REPLY_BY_ID", "name": "GET_COMMENT_REPLY_BY_ID", "setter_type": null, "type": "builtins.str"}}, "UPDATE_COMMENT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "comment.queries.UPDATE_COMMENT", "name": "UPDATE_COMMENT", "setter_type": null, "type": "builtins.str"}}, "UPDATE_COMMENT_REPLY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "comment.queries.UPDATE_COMMENT_REPLY", "name": "UPDATE_COMMENT_REPLY", "setter_type": null, "type": "builtins.str"}}, "UPDATE_COMMENT_STATUS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "comment.queries.UPDATE_COMMENT_STATUS", "name": "UPDATE_COMMENT_STATUS", "setter_type": null, "type": "builtins.str"}}, "VERIFY_COMMENT_OWNERSHIP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "comment.queries.VERIFY_COMMENT_OWNERSHIP", "name": "VERIFY_COMMENT_OWNERSHIP", "setter_type": null, "type": "builtins.str"}}, "VERIFY_REPLY_OWNERSHIP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "comment.queries.VERIFY_REPLY_OWNERSHIP", "name": "VERIFY_REPLY_OWNERSHIP", "setter_type": null, "type": "builtins.str"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "comment.queries.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "comment.queries.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "comment.queries.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "comment.queries.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "comment.queries.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "comment.queries.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/Users/<USER>/impact/pricesmart-promo-v3/backend/comment/queries.py"}