{".class": "MypyFile", "_fullname": "marketing_calendar.queries", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "FILTER_BY_IDS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "marketing_calendar.queries.FILTER_BY_IDS_QUERY", "name": "FILTER_BY_IDS_QUERY", "setter_type": null, "type": "builtins.str"}}, "GET_EVENTS_USING_HIERARCHY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "marketing_calendar.queries.GET_EVENTS_USING_HIERARCHY", "name": "GET_EVENTS_USING_HIERARCHY", "setter_type": null, "type": "builtins.str"}}, "GET_EVENTS_USING_HIERARCHY_WITH_METRICS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "marketing_calendar.queries.GET_EVENTS_USING_HIERARCHY_WITH_METRICS", "name": "GET_EVENTS_USING_HIERARCHY_WITH_METRICS", "setter_type": null, "type": "builtins.str"}}, "GET_EVENTS_USING_IDS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "marketing_calendar.queries.GET_EVENTS_USING_IDS", "name": "GET_EVENTS_USING_IDS", "setter_type": null, "type": "builtins.str"}}, "GET_EVENTS_WITH_METRICS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "marketing_calendar.queries.GET_EVENTS_WITH_METRICS", "name": "GET_EVENTS_WITH_METRICS", "setter_type": null, "type": "builtins.str"}}, "GET_PROMOS_DATA_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "marketing_calendar.queries.GET_PROMOS_DATA_QUERY", "name": "GET_PROMOS_DATA_QUERY", "setter_type": null, "type": "builtins.str"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "marketing_calendar.queries.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "marketing_calendar.queries.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "marketing_calendar.queries.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "marketing_calendar.queries.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "marketing_calendar.queries.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "marketing_calendar.queries.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/Users/<USER>/impact/pricesmart-promo-v3/backend/marketing_calendar/queries.py"}