{".class": "MypyFile", "_fullname": "h11._abnf", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "HEXDIG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "h11._abnf.HEXDIG", "name": "HEXDIG", "setter_type": null, "type": "builtins.str"}}, "OWS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "h11._abnf.OWS", "name": "OWS", "setter_type": null, "type": "builtins.str"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "h11._abnf.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "h11._abnf.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "h11._abnf.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "h11._abnf.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "h11._abnf.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "h11._abnf.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "chunk_ext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "h11._abnf.chunk_ext", "name": "chunk_ext", "setter_type": null, "type": "builtins.str"}}, "chunk_header": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "h11._abnf.chunk_header", "name": "chunk_header", "setter_type": null, "type": "builtins.str"}}, "chunk_size": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "h11._abnf.chunk_size", "name": "chunk_size", "setter_type": null, "type": "builtins.str"}}, "field_content": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "h11._abnf.field_content", "name": "field_content", "setter_type": null, "type": "builtins.str"}}, "field_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "h11._abnf.field_name", "name": "field_name", "setter_type": null, "type": "builtins.str"}}, "field_value": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "h11._abnf.field_value", "name": "field_value", "setter_type": null, "type": "builtins.str"}}, "field_vchar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "h11._abnf.field_vchar", "name": "field_vchar", "setter_type": null, "type": "builtins.str"}}, "header_field": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "h11._abnf.header_field", "name": "header_field", "setter_type": null, "type": "builtins.str"}}, "http_version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "h11._abnf.http_version", "name": "http_version", "setter_type": null, "type": "builtins.str"}}, "method": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "h11._abnf.method", "name": "method", "setter_type": null, "type": "builtins.str"}}, "reason_phrase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "h11._abnf.reason_phrase", "name": "reason_phrase", "setter_type": null, "type": "builtins.str"}}, "request_line": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "h11._abnf.request_line", "name": "request_line", "setter_type": null, "type": "builtins.str"}}, "request_target": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "h11._abnf.request_target", "name": "request_target", "setter_type": null, "type": "builtins.str"}}, "status_code": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "h11._abnf.status_code", "name": "status_code", "setter_type": null, "type": "builtins.str"}}, "status_line": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "h11._abnf.status_line", "name": "status_line", "setter_type": null, "type": "builtins.str"}}, "token": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "h11._abnf.token", "name": "token", "setter_type": null, "type": "builtins.str"}}, "vchar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "h11._abnf.vchar", "name": "vchar", "setter_type": null, "type": "builtins.str"}}, "vchar_or_obs_text": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "h11._abnf.vchar_or_obs_text", "name": "vchar_or_obs_text", "setter_type": null, "type": "builtins.str"}}}, "path": "/Users/<USER>/.pyenv/versions/3.12/lib/python3.12/site-packages/h11/_abnf.py"}