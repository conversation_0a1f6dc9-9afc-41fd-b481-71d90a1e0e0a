{".class": "MypyFile", "_fullname": "google.cloud.secretmanager_v1.types.resources", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CustomerManagedEncryption": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.secretmanager_v1.types.resources.CustomerManagedEncryption", "name": "CustomerManagedEncryption", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.cloud.secretmanager_v1.types.resources.CustomerManagedEncryption", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.secretmanager_v1.types.resources", "mro": ["google.cloud.secretmanager_v1.types.resources.CustomerManagedEncryption", "builtins.object"], "names": {".class": "SymbolTable", "kms_key_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.CustomerManagedEncryption.kms_key_name", "name": "kms_key_name", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.secretmanager_v1.types.resources.CustomerManagedEncryption.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.secretmanager_v1.types.resources.CustomerManagedEncryption", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CustomerManagedEncryptionStatus": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.secretmanager_v1.types.resources.CustomerManagedEncryptionStatus", "name": "CustomerManagedEncryptionStatus", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.cloud.secretmanager_v1.types.resources.CustomerManagedEncryptionStatus", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.secretmanager_v1.types.resources", "mro": ["google.cloud.secretmanager_v1.types.resources.CustomerManagedEncryptionStatus", "builtins.object"], "names": {".class": "SymbolTable", "kms_key_version_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.CustomerManagedEncryptionStatus.kms_key_version_name", "name": "kms_key_version_name", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.secretmanager_v1.types.resources.CustomerManagedEncryptionStatus.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.secretmanager_v1.types.resources.CustomerManagedEncryptionStatus", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MutableMapping": {".class": "SymbolTableNode", "cross_ref": "typing.MutableMapping", "kind": "Gdef", "module_public": false}, "MutableSequence": {".class": "SymbolTableNode", "cross_ref": "typing.MutableSequence", "kind": "Gdef", "module_public": false}, "Replication": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.secretmanager_v1.types.resources.Replication", "name": "Replication", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.cloud.secretmanager_v1.types.resources.Replication", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.secretmanager_v1.types.resources", "mro": ["google.cloud.secretmanager_v1.types.resources.Replication", "builtins.object"], "names": {".class": "SymbolTable", "Automatic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.secretmanager_v1.types.resources.Replication.Automatic", "name": "Automatic", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.cloud.secretmanager_v1.types.resources.Replication.Automatic", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.secretmanager_v1.types.resources", "mro": ["google.cloud.secretmanager_v1.types.resources.Replication.Automatic", "builtins.object"], "names": {".class": "SymbolTable", "customer_managed_encryption": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.Replication.Automatic.customer_managed_encryption", "name": "customer_managed_encryption", "setter_type": null, "type": "google.cloud.secretmanager_v1.types.resources.CustomerManagedEncryption"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.secretmanager_v1.types.resources.Replication.Automatic.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.secretmanager_v1.types.resources.Replication.Automatic", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UserManaged": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.secretmanager_v1.types.resources.Replication.UserManaged", "name": "UserManaged", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.cloud.secretmanager_v1.types.resources.Replication.UserManaged", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.secretmanager_v1.types.resources", "mro": ["google.cloud.secretmanager_v1.types.resources.Replication.UserManaged", "builtins.object"], "names": {".class": "SymbolTable", "Replica": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.secretmanager_v1.types.resources.Replication.UserManaged.Replica", "name": "Replica", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.cloud.secretmanager_v1.types.resources.Replication.UserManaged.Replica", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.secretmanager_v1.types.resources", "mro": ["google.cloud.secretmanager_v1.types.resources.Replication.UserManaged.Replica", "builtins.object"], "names": {".class": "SymbolTable", "customer_managed_encryption": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.Replication.UserManaged.Replica.customer_managed_encryption", "name": "customer_managed_encryption", "setter_type": null, "type": "google.cloud.secretmanager_v1.types.resources.CustomerManagedEncryption"}}, "location": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.Replication.UserManaged.Replica.location", "name": "location", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.secretmanager_v1.types.resources.Replication.UserManaged.Replica.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.secretmanager_v1.types.resources.Replication.UserManaged.Replica", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "replicas": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.Replication.UserManaged.replicas", "name": "replicas", "setter_type": null, "type": {".class": "Instance", "args": ["google.cloud.secretmanager_v1.types.resources.Replication.UserManaged.Replica"], "extra_attrs": null, "type_ref": "typing.MutableSequence"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.secretmanager_v1.types.resources.Replication.UserManaged.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.secretmanager_v1.types.resources.Replication.UserManaged", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "automatic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.Replication.automatic", "name": "automatic", "setter_type": null, "type": "google.cloud.secretmanager_v1.types.resources.Replication.Automatic"}}, "user_managed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.Replication.user_managed", "name": "user_managed", "setter_type": null, "type": "google.cloud.secretmanager_v1.types.resources.Replication.UserManaged"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.secretmanager_v1.types.resources.Replication.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.secretmanager_v1.types.resources.Replication", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ReplicationStatus": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.secretmanager_v1.types.resources.ReplicationStatus", "name": "ReplicationStatus", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.cloud.secretmanager_v1.types.resources.ReplicationStatus", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.secretmanager_v1.types.resources", "mro": ["google.cloud.secretmanager_v1.types.resources.ReplicationStatus", "builtins.object"], "names": {".class": "SymbolTable", "AutomaticStatus": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.secretmanager_v1.types.resources.ReplicationStatus.AutomaticStatus", "name": "AutomaticStatus", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.cloud.secretmanager_v1.types.resources.ReplicationStatus.AutomaticStatus", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.secretmanager_v1.types.resources", "mro": ["google.cloud.secretmanager_v1.types.resources.ReplicationStatus.AutomaticStatus", "builtins.object"], "names": {".class": "SymbolTable", "customer_managed_encryption": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.ReplicationStatus.AutomaticStatus.customer_managed_encryption", "name": "customer_managed_encryption", "setter_type": null, "type": "google.cloud.secretmanager_v1.types.resources.CustomerManagedEncryptionStatus"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.secretmanager_v1.types.resources.ReplicationStatus.AutomaticStatus.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.secretmanager_v1.types.resources.ReplicationStatus.AutomaticStatus", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UserManagedStatus": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.secretmanager_v1.types.resources.ReplicationStatus.UserManagedStatus", "name": "UserManagedStatus", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.cloud.secretmanager_v1.types.resources.ReplicationStatus.UserManagedStatus", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.secretmanager_v1.types.resources", "mro": ["google.cloud.secretmanager_v1.types.resources.ReplicationStatus.UserManagedStatus", "builtins.object"], "names": {".class": "SymbolTable", "ReplicaStatus": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.secretmanager_v1.types.resources.ReplicationStatus.UserManagedStatus.ReplicaStatus", "name": "ReplicaStatus", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.cloud.secretmanager_v1.types.resources.ReplicationStatus.UserManagedStatus.ReplicaStatus", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.secretmanager_v1.types.resources", "mro": ["google.cloud.secretmanager_v1.types.resources.ReplicationStatus.UserManagedStatus.ReplicaStatus", "builtins.object"], "names": {".class": "SymbolTable", "customer_managed_encryption": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.ReplicationStatus.UserManagedStatus.ReplicaStatus.customer_managed_encryption", "name": "customer_managed_encryption", "setter_type": null, "type": "google.cloud.secretmanager_v1.types.resources.CustomerManagedEncryptionStatus"}}, "location": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.ReplicationStatus.UserManagedStatus.ReplicaStatus.location", "name": "location", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.secretmanager_v1.types.resources.ReplicationStatus.UserManagedStatus.ReplicaStatus.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.secretmanager_v1.types.resources.ReplicationStatus.UserManagedStatus.ReplicaStatus", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "replicas": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.ReplicationStatus.UserManagedStatus.replicas", "name": "replicas", "setter_type": null, "type": {".class": "Instance", "args": ["google.cloud.secretmanager_v1.types.resources.ReplicationStatus.UserManagedStatus.ReplicaStatus"], "extra_attrs": null, "type_ref": "typing.MutableSequence"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.secretmanager_v1.types.resources.ReplicationStatus.UserManagedStatus.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.secretmanager_v1.types.resources.ReplicationStatus.UserManagedStatus", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "automatic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.ReplicationStatus.automatic", "name": "automatic", "setter_type": null, "type": "google.cloud.secretmanager_v1.types.resources.ReplicationStatus.AutomaticStatus"}}, "user_managed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.ReplicationStatus.user_managed", "name": "user_managed", "setter_type": null, "type": "google.cloud.secretmanager_v1.types.resources.ReplicationStatus.UserManagedStatus"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.secretmanager_v1.types.resources.ReplicationStatus.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.secretmanager_v1.types.resources.ReplicationStatus", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Rotation": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.secretmanager_v1.types.resources.Rotation", "name": "Rotation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.cloud.secretmanager_v1.types.resources.Rotation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.secretmanager_v1.types.resources", "mro": ["google.cloud.secretmanager_v1.types.resources.Rotation", "builtins.object"], "names": {".class": "SymbolTable", "next_rotation_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.Rotation.next_rotation_time", "name": "next_rotation_time", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.secretmanager_v1.types.resources.timestamp_pb2", "source_any": null, "type_of_any": 3}}}, "rotation_period": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.Rotation.rotation_period", "name": "rotation_period", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.secretmanager_v1.types.resources.duration_pb2", "source_any": null, "type_of_any": 3}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.secretmanager_v1.types.resources.Rotation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.secretmanager_v1.types.resources.Rotation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Secret": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.secretmanager_v1.types.resources.Secret", "name": "Secret", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.cloud.secretmanager_v1.types.resources.Secret", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.secretmanager_v1.types.resources", "mro": ["google.cloud.secretmanager_v1.types.resources.Secret", "builtins.object"], "names": {".class": "SymbolTable", "annotations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.Secret.annotations", "name": "annotations", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.MutableMapping"}}}, "create_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.Secret.create_time", "name": "create_time", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.secretmanager_v1.types.resources.timestamp_pb2", "source_any": null, "type_of_any": 3}}}, "customer_managed_encryption": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.Secret.customer_managed_encryption", "name": "customer_managed_encryption", "setter_type": null, "type": "google.cloud.secretmanager_v1.types.resources.CustomerManagedEncryption"}}, "etag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.Secret.etag", "name": "etag", "setter_type": null, "type": "builtins.str"}}, "expire_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.Secret.expire_time", "name": "expire_time", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.secretmanager_v1.types.resources.timestamp_pb2", "source_any": null, "type_of_any": 3}}}, "labels": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.Secret.labels", "name": "labels", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.MutableMapping"}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.Secret.name", "name": "name", "setter_type": null, "type": "builtins.str"}}, "replication": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.Secret.replication", "name": "replication", "setter_type": null, "type": "google.cloud.secretmanager_v1.types.resources.Replication"}}, "rotation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.Secret.rotation", "name": "rotation", "setter_type": null, "type": "google.cloud.secretmanager_v1.types.resources.Rotation"}}, "topics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.Secret.topics", "name": "topics", "setter_type": null, "type": {".class": "Instance", "args": ["google.cloud.secretmanager_v1.types.resources.Topic"], "extra_attrs": null, "type_ref": "typing.MutableSequence"}}}, "ttl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.Secret.ttl", "name": "ttl", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.secretmanager_v1.types.resources.duration_pb2", "source_any": null, "type_of_any": 3}}}, "version_aliases": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.Secret.version_aliases", "name": "version_aliases", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "typing.MutableMapping"}}}, "version_destroy_ttl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.Secret.version_destroy_ttl", "name": "version_destroy_ttl", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.secretmanager_v1.types.resources.duration_pb2", "source_any": null, "type_of_any": 3}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.secretmanager_v1.types.resources.Secret.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.secretmanager_v1.types.resources.Secret", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SecretPayload": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.secretmanager_v1.types.resources.SecretPayload", "name": "SecretPayload", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.cloud.secretmanager_v1.types.resources.SecretPayload", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.secretmanager_v1.types.resources", "mro": ["google.cloud.secretmanager_v1.types.resources.SecretPayload", "builtins.object"], "names": {".class": "SymbolTable", "data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.SecretPayload.data", "name": "data", "setter_type": null, "type": "builtins.bytes"}}, "data_crc32c": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.SecretPayload.data_crc32c", "name": "data_crc32c", "setter_type": null, "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.secretmanager_v1.types.resources.SecretPayload.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.secretmanager_v1.types.resources.SecretPayload", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SecretVersion": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.secretmanager_v1.types.resources.SecretVersion", "name": "SecretVersion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.cloud.secretmanager_v1.types.resources.SecretVersion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.secretmanager_v1.types.resources", "mro": ["google.cloud.secretmanager_v1.types.resources.SecretVersion", "builtins.object"], "names": {".class": "SymbolTable", "State": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.secretmanager_v1.types.resources.SecretVersion.State", "name": "State", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.cloud.secretmanager_v1.types.resources.SecretVersion.State", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.secretmanager_v1.types.resources", "mro": ["google.cloud.secretmanager_v1.types.resources.SecretVersion.State", "builtins.object"], "names": {".class": "SymbolTable", "DESTROYED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.SecretVersion.State.DESTROYED", "name": "DESTROYED", "setter_type": null, "type": "builtins.int"}}, "DISABLED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.SecretVersion.State.DISABLED", "name": "DISABLED", "setter_type": null, "type": "builtins.int"}}, "ENABLED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.SecretVersion.State.ENABLED", "name": "ENABLED", "setter_type": null, "type": "builtins.int"}}, "STATE_UNSPECIFIED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.SecretVersion.State.STATE_UNSPECIFIED", "name": "STATE_UNSPECIFIED", "setter_type": null, "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.secretmanager_v1.types.resources.SecretVersion.State.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.secretmanager_v1.types.resources.SecretVersion.State", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "client_specified_payload_checksum": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.SecretVersion.client_specified_payload_checksum", "name": "client_specified_payload_checksum", "setter_type": null, "type": "builtins.bool"}}, "create_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.SecretVersion.create_time", "name": "create_time", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.secretmanager_v1.types.resources.timestamp_pb2", "source_any": null, "type_of_any": 3}}}, "customer_managed_encryption": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.SecretVersion.customer_managed_encryption", "name": "customer_managed_encryption", "setter_type": null, "type": "google.cloud.secretmanager_v1.types.resources.CustomerManagedEncryptionStatus"}}, "destroy_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.SecretVersion.destroy_time", "name": "destroy_time", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.secretmanager_v1.types.resources.timestamp_pb2", "source_any": null, "type_of_any": 3}}}, "etag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.SecretVersion.etag", "name": "etag", "setter_type": null, "type": "builtins.str"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.SecretVersion.name", "name": "name", "setter_type": null, "type": "builtins.str"}}, "replication_status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.SecretVersion.replication_status", "name": "replication_status", "setter_type": null, "type": "google.cloud.secretmanager_v1.types.resources.ReplicationStatus"}}, "scheduled_destroy_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.SecretVersion.scheduled_destroy_time", "name": "scheduled_destroy_time", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.secretmanager_v1.types.resources.timestamp_pb2", "source_any": null, "type_of_any": 3}}}, "state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.SecretVersion.state", "name": "state", "setter_type": null, "type": "google.cloud.secretmanager_v1.types.resources.SecretVersion.State"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.secretmanager_v1.types.resources.SecretVersion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.secretmanager_v1.types.resources.SecretVersion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Topic": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.secretmanager_v1.types.resources.Topic", "name": "Topic", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.cloud.secretmanager_v1.types.resources.Topic", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.secretmanager_v1.types.resources", "mro": ["google.cloud.secretmanager_v1.types.resources.Topic", "builtins.object"], "names": {".class": "SymbolTable", "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.Topic.name", "name": "name", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.secretmanager_v1.types.resources.Topic.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.secretmanager_v1.types.resources.Topic", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "google.cloud.secretmanager_v1.types.resources.proto", "source_any": {".class": "AnyType", "missing_import_name": "google.cloud.secretmanager_v1.types.resources.proto", "source_any": null, "type_of_any": 3}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.secretmanager_v1.types.resources.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.secretmanager_v1.types.resources.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.secretmanager_v1.types.resources.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.secretmanager_v1.types.resources.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.secretmanager_v1.types.resources.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__protobuf__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.resources.__protobuf__", "name": "__protobuf__", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.secretmanager_v1.types.resources.proto", "source_any": {".class": "AnyType", "missing_import_name": "google.cloud.secretmanager_v1.types.resources.proto", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.secretmanager_v1.types.resources.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "duration_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.secretmanager_v1.types.resources.duration_pb2", "name": "duration_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.secretmanager_v1.types.resources.duration_pb2", "source_any": null, "type_of_any": 3}}}, "proto": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.secretmanager_v1.types.resources.proto", "name": "proto", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.secretmanager_v1.types.resources.proto", "source_any": null, "type_of_any": 3}}}, "timestamp_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.secretmanager_v1.types.resources.timestamp_pb2", "name": "timestamp_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.secretmanager_v1.types.resources.timestamp_pb2", "source_any": null, "type_of_any": 3}}}}, "path": "/Users/<USER>/.pyenv/versions/3.12/lib/python3.12/site-packages/google/cloud/secretmanager_v1/types/resources.py"}