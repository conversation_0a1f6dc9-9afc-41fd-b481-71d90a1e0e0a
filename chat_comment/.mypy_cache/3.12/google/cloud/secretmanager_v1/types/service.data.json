{".class": "MypyFile", "_fullname": "google.cloud.secretmanager_v1.types.service", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AccessSecretVersionRequest": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.secretmanager_v1.types.service.AccessSecretVersionRequest", "name": "AccessSecretVersionRequest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.cloud.secretmanager_v1.types.service.AccessSecretVersionRequest", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.secretmanager_v1.types.service", "mro": ["google.cloud.secretmanager_v1.types.service.AccessSecretVersionRequest", "builtins.object"], "names": {".class": "SymbolTable", "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.AccessSecretVersionRequest.name", "name": "name", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.secretmanager_v1.types.service.AccessSecretVersionRequest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.secretmanager_v1.types.service.AccessSecretVersionRequest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AccessSecretVersionResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.secretmanager_v1.types.service.AccessSecretVersionResponse", "name": "AccessSecretVersionResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.cloud.secretmanager_v1.types.service.AccessSecretVersionResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.secretmanager_v1.types.service", "mro": ["google.cloud.secretmanager_v1.types.service.AccessSecretVersionResponse", "builtins.object"], "names": {".class": "SymbolTable", "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.AccessSecretVersionResponse.name", "name": "name", "setter_type": null, "type": "builtins.str"}}, "payload": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.AccessSecretVersionResponse.payload", "name": "payload", "setter_type": null, "type": "google.cloud.secretmanager_v1.types.resources.SecretPayload"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.secretmanager_v1.types.service.AccessSecretVersionResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.secretmanager_v1.types.service.AccessSecretVersionResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AddSecretVersionRequest": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.secretmanager_v1.types.service.AddSecretVersionRequest", "name": "AddSecretVersionRequest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.cloud.secretmanager_v1.types.service.AddSecretVersionRequest", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.secretmanager_v1.types.service", "mro": ["google.cloud.secretmanager_v1.types.service.AddSecretVersionRequest", "builtins.object"], "names": {".class": "SymbolTable", "parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.AddSecretVersionRequest.parent", "name": "parent", "setter_type": null, "type": "builtins.str"}}, "payload": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.AddSecretVersionRequest.payload", "name": "payload", "setter_type": null, "type": "google.cloud.secretmanager_v1.types.resources.SecretPayload"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.secretmanager_v1.types.service.AddSecretVersionRequest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.secretmanager_v1.types.service.AddSecretVersionRequest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CreateSecretRequest": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.secretmanager_v1.types.service.CreateSecretRequest", "name": "CreateSecretRequest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.cloud.secretmanager_v1.types.service.CreateSecretRequest", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.secretmanager_v1.types.service", "mro": ["google.cloud.secretmanager_v1.types.service.CreateSecretRequest", "builtins.object"], "names": {".class": "SymbolTable", "parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.CreateSecretRequest.parent", "name": "parent", "setter_type": null, "type": "builtins.str"}}, "secret": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.CreateSecretRequest.secret", "name": "secret", "setter_type": null, "type": "google.cloud.secretmanager_v1.types.resources.Secret"}}, "secret_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.CreateSecretRequest.secret_id", "name": "secret_id", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.secretmanager_v1.types.service.CreateSecretRequest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.secretmanager_v1.types.service.CreateSecretRequest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DeleteSecretRequest": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.secretmanager_v1.types.service.DeleteSecretRequest", "name": "DeleteSecretRequest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.cloud.secretmanager_v1.types.service.DeleteSecretRequest", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.secretmanager_v1.types.service", "mro": ["google.cloud.secretmanager_v1.types.service.DeleteSecretRequest", "builtins.object"], "names": {".class": "SymbolTable", "etag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.DeleteSecretRequest.etag", "name": "etag", "setter_type": null, "type": "builtins.str"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.DeleteSecretRequest.name", "name": "name", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.secretmanager_v1.types.service.DeleteSecretRequest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.secretmanager_v1.types.service.DeleteSecretRequest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DestroySecretVersionRequest": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.secretmanager_v1.types.service.DestroySecretVersionRequest", "name": "DestroySecretVersionRequest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.cloud.secretmanager_v1.types.service.DestroySecretVersionRequest", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.secretmanager_v1.types.service", "mro": ["google.cloud.secretmanager_v1.types.service.DestroySecretVersionRequest", "builtins.object"], "names": {".class": "SymbolTable", "etag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.DestroySecretVersionRequest.etag", "name": "etag", "setter_type": null, "type": "builtins.str"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.DestroySecretVersionRequest.name", "name": "name", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.secretmanager_v1.types.service.DestroySecretVersionRequest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.secretmanager_v1.types.service.DestroySecretVersionRequest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DisableSecretVersionRequest": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.secretmanager_v1.types.service.DisableSecretVersionRequest", "name": "DisableSecretVersionRequest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.cloud.secretmanager_v1.types.service.DisableSecretVersionRequest", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.secretmanager_v1.types.service", "mro": ["google.cloud.secretmanager_v1.types.service.DisableSecretVersionRequest", "builtins.object"], "names": {".class": "SymbolTable", "etag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.DisableSecretVersionRequest.etag", "name": "etag", "setter_type": null, "type": "builtins.str"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.DisableSecretVersionRequest.name", "name": "name", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.secretmanager_v1.types.service.DisableSecretVersionRequest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.secretmanager_v1.types.service.DisableSecretVersionRequest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EnableSecretVersionRequest": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.secretmanager_v1.types.service.EnableSecretVersionRequest", "name": "EnableSecretVersionRequest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.cloud.secretmanager_v1.types.service.EnableSecretVersionRequest", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.secretmanager_v1.types.service", "mro": ["google.cloud.secretmanager_v1.types.service.EnableSecretVersionRequest", "builtins.object"], "names": {".class": "SymbolTable", "etag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.EnableSecretVersionRequest.etag", "name": "etag", "setter_type": null, "type": "builtins.str"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.EnableSecretVersionRequest.name", "name": "name", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.secretmanager_v1.types.service.EnableSecretVersionRequest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.secretmanager_v1.types.service.EnableSecretVersionRequest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GetSecretRequest": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.secretmanager_v1.types.service.GetSecretRequest", "name": "GetSecretRequest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.cloud.secretmanager_v1.types.service.GetSecretRequest", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.secretmanager_v1.types.service", "mro": ["google.cloud.secretmanager_v1.types.service.GetSecretRequest", "builtins.object"], "names": {".class": "SymbolTable", "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.GetSecretRequest.name", "name": "name", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.secretmanager_v1.types.service.GetSecretRequest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.secretmanager_v1.types.service.GetSecretRequest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GetSecretVersionRequest": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.secretmanager_v1.types.service.GetSecretVersionRequest", "name": "GetSecretVersionRequest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.cloud.secretmanager_v1.types.service.GetSecretVersionRequest", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.secretmanager_v1.types.service", "mro": ["google.cloud.secretmanager_v1.types.service.GetSecretVersionRequest", "builtins.object"], "names": {".class": "SymbolTable", "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.GetSecretVersionRequest.name", "name": "name", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.secretmanager_v1.types.service.GetSecretVersionRequest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.secretmanager_v1.types.service.GetSecretVersionRequest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ListSecretVersionsRequest": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.secretmanager_v1.types.service.ListSecretVersionsRequest", "name": "ListSecretVersionsRequest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.cloud.secretmanager_v1.types.service.ListSecretVersionsRequest", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.secretmanager_v1.types.service", "mro": ["google.cloud.secretmanager_v1.types.service.ListSecretVersionsRequest", "builtins.object"], "names": {".class": "SymbolTable", "filter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.ListSecretVersionsRequest.filter", "name": "filter", "setter_type": null, "type": "builtins.str"}}, "page_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.ListSecretVersionsRequest.page_size", "name": "page_size", "setter_type": null, "type": "builtins.int"}}, "page_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.ListSecretVersionsRequest.page_token", "name": "page_token", "setter_type": null, "type": "builtins.str"}}, "parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.ListSecretVersionsRequest.parent", "name": "parent", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.secretmanager_v1.types.service.ListSecretVersionsRequest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.secretmanager_v1.types.service.ListSecretVersionsRequest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ListSecretVersionsResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.secretmanager_v1.types.service.ListSecretVersionsResponse", "name": "ListSecretVersionsResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.cloud.secretmanager_v1.types.service.ListSecretVersionsResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.secretmanager_v1.types.service", "mro": ["google.cloud.secretmanager_v1.types.service.ListSecretVersionsResponse", "builtins.object"], "names": {".class": "SymbolTable", "next_page_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.ListSecretVersionsResponse.next_page_token", "name": "next_page_token", "setter_type": null, "type": "builtins.str"}}, "raw_page": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.secretmanager_v1.types.service.ListSecretVersionsResponse.raw_page", "name": "raw_page", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.secretmanager_v1.types.service.ListSecretVersionsResponse.raw_page", "name": "raw_page", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.secretmanager_v1.types.service.ListSecretVersionsResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "raw_page of ListSecretVersionsResponse", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "total_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.ListSecretVersionsResponse.total_size", "name": "total_size", "setter_type": null, "type": "builtins.int"}}, "versions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.ListSecretVersionsResponse.versions", "name": "versions", "setter_type": null, "type": {".class": "Instance", "args": ["google.cloud.secretmanager_v1.types.resources.SecretVersion"], "extra_attrs": null, "type_ref": "typing.MutableSequence"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.secretmanager_v1.types.service.ListSecretVersionsResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.secretmanager_v1.types.service.ListSecretVersionsResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ListSecretsRequest": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.secretmanager_v1.types.service.ListSecretsRequest", "name": "ListSecretsRequest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.cloud.secretmanager_v1.types.service.ListSecretsRequest", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.secretmanager_v1.types.service", "mro": ["google.cloud.secretmanager_v1.types.service.ListSecretsRequest", "builtins.object"], "names": {".class": "SymbolTable", "filter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.ListSecretsRequest.filter", "name": "filter", "setter_type": null, "type": "builtins.str"}}, "page_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.ListSecretsRequest.page_size", "name": "page_size", "setter_type": null, "type": "builtins.int"}}, "page_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.ListSecretsRequest.page_token", "name": "page_token", "setter_type": null, "type": "builtins.str"}}, "parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.ListSecretsRequest.parent", "name": "parent", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.secretmanager_v1.types.service.ListSecretsRequest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.secretmanager_v1.types.service.ListSecretsRequest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ListSecretsResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.secretmanager_v1.types.service.ListSecretsResponse", "name": "ListSecretsResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.cloud.secretmanager_v1.types.service.ListSecretsResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.secretmanager_v1.types.service", "mro": ["google.cloud.secretmanager_v1.types.service.ListSecretsResponse", "builtins.object"], "names": {".class": "SymbolTable", "next_page_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.ListSecretsResponse.next_page_token", "name": "next_page_token", "setter_type": null, "type": "builtins.str"}}, "raw_page": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.secretmanager_v1.types.service.ListSecretsResponse.raw_page", "name": "raw_page", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.secretmanager_v1.types.service.ListSecretsResponse.raw_page", "name": "raw_page", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.secretmanager_v1.types.service.ListSecretsResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "raw_page of ListSecretsResponse", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "secrets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.ListSecretsResponse.secrets", "name": "secrets", "setter_type": null, "type": {".class": "Instance", "args": ["google.cloud.secretmanager_v1.types.resources.Secret"], "extra_attrs": null, "type_ref": "typing.MutableSequence"}}}, "total_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.ListSecretsResponse.total_size", "name": "total_size", "setter_type": null, "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.secretmanager_v1.types.service.ListSecretsResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.secretmanager_v1.types.service.ListSecretsResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MutableMapping": {".class": "SymbolTableNode", "cross_ref": "typing.MutableMapping", "kind": "Gdef", "module_public": false}, "MutableSequence": {".class": "SymbolTableNode", "cross_ref": "typing.MutableSequence", "kind": "Gdef", "module_public": false}, "UpdateSecretRequest": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.secretmanager_v1.types.service.UpdateSecretRequest", "name": "UpdateSecretRequest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.cloud.secretmanager_v1.types.service.UpdateSecretRequest", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.secretmanager_v1.types.service", "mro": ["google.cloud.secretmanager_v1.types.service.UpdateSecretRequest", "builtins.object"], "names": {".class": "SymbolTable", "secret": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.UpdateSecretRequest.secret", "name": "secret", "setter_type": null, "type": "google.cloud.secretmanager_v1.types.resources.Secret"}}, "update_mask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.UpdateSecretRequest.update_mask", "name": "update_mask", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.secretmanager_v1.types.service.field_mask_pb2", "source_any": null, "type_of_any": 3}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.secretmanager_v1.types.service.UpdateSecretRequest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.secretmanager_v1.types.service.UpdateSecretRequest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "google.cloud.secretmanager_v1.types.service.proto", "source_any": {".class": "AnyType", "missing_import_name": "google.cloud.secretmanager_v1.types.service.proto", "source_any": null, "type_of_any": 3}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.secretmanager_v1.types.service.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.secretmanager_v1.types.service.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.secretmanager_v1.types.service.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.secretmanager_v1.types.service.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.secretmanager_v1.types.service.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__protobuf__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.cloud.secretmanager_v1.types.service.__protobuf__", "name": "__protobuf__", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.secretmanager_v1.types.service.proto", "source_any": {".class": "AnyType", "missing_import_name": "google.cloud.secretmanager_v1.types.service.proto", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.secretmanager_v1.types.service.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "field_mask_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.secretmanager_v1.types.service.field_mask_pb2", "name": "field_mask_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.secretmanager_v1.types.service.field_mask_pb2", "source_any": null, "type_of_any": 3}}}, "proto": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.secretmanager_v1.types.service.proto", "name": "proto", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.secretmanager_v1.types.service.proto", "source_any": null, "type_of_any": 3}}}, "resources": {".class": "SymbolTableNode", "cross_ref": "google.cloud.secretmanager_v1.types.resources", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/.pyenv/versions/3.12/lib/python3.12/site-packages/google/cloud/secretmanager_v1/types/service.py"}