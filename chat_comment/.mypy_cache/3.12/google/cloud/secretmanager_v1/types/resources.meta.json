{"data_mtime": 1757043297, "dep_lines": [16, 18, 1, 1, 1, 1, 20, 22], "dep_prios": [5, 5, 5, 30, 30, 30, 5, 10], "dependencies": ["__future__", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc"], "hash": "7955173acdca81dc4ae6dc7bdb4820663d103f91", "id": "google.cloud.secretmanager_v1.types.resources", "ignore_all": true, "interface_hash": "707be151774d1d3fb85c82fd0baecd502d332428", "mtime": 1753080531, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/.pyenv/versions/3.12/lib/python3.12/site-packages/google/cloud/secretmanager_v1/types/resources.py", "plugin_data": null, "size": 29614, "suppressed": ["google.protobuf", "proto"], "version_id": "1.17.1"}