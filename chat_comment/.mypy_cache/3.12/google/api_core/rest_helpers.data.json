{".class": "MypyFile", "_fullname": "google.api_core.rest_helpers", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.rest_helpers.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.rest_helpers.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.rest_helpers.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.rest_helpers.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.rest_helpers.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.rest_helpers.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_canonicalize": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["obj", "strict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.rest_helpers._canonicalize", "name": "_canonicalize", "type": null}}, "_flatten": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["obj", "key_path", "strict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.rest_helpers._flatten", "name": "_flatten", "type": null}}, "_flatten_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["obj", "key_path", "strict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.rest_helpers._flatten_dict", "name": "_flatten_dict", "type": null}}, "_flatten_list": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["elems", "key_path", "strict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.rest_helpers._flatten_list", "name": "_flatten_list", "type": null}}, "_flatten_value": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["obj", "key_path", "strict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.rest_helpers._flatten_value", "name": "_flatten_value", "type": null}}, "_is_primitive_value": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.rest_helpers._is_primitive_value", "name": "_is_primitive_value", "type": null}}, "flatten_query_params": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["obj", "strict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.rest_helpers.flatten_query_params", "name": "flatten_query_params", "type": null}}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef"}}, "path": "/Users/<USER>/.pyenv/versions/3.12/lib/python3.12/site-packages/google/api_core/rest_helpers.py"}