{"data_mtime": 1757043297, "dep_lines": [17, 18, 19, 23, 1, 1, 1, 23, 24, 23, 21, 22], "dep_prios": [5, 10, 5, 20, 5, 30, 30, 10, 5, 20, 10, 10], "dependencies": ["collections", "string", "typing", "google", "builtins", "_frozen_importlib", "abc"], "hash": "382d242e036c6a44a1626217281c53f2fb61126a", "id": "google.api_core.rest_streaming", "ignore_all": true, "interface_hash": "500e354afa7ad27e972cae0c79e147ddf4f374c2", "mtime": 1753080525, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/.pyenv/versions/3.12/lib/python3.12/site-packages/google/api_core/rest_streaming.py", "plugin_data": null, "size": 4948, "suppressed": ["google.protobuf.message", "google.protobuf.json_format", "google.protobuf", "proto", "requests"], "version_id": "1.17.1"}