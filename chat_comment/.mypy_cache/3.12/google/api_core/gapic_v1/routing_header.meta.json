{"data_mtime": **********, "dep_lines": [25, 23, 24, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 30, 30, 30], "dependencies": ["urllib.parse", "functools", "enum", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "de97584345fd10c1e83b61a27f49b4e3941eaefa", "id": "google.api_core.gapic_v1.routing_header", "ignore_all": true, "interface_hash": "db920b76251ac48b29c87a7515dfdb015cca692b", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/.pyenv/versions/3.12/lib/python3.12/site-packages/google/api_core/gapic_v1/routing_header.py", "plugin_data": null, "size": 3093, "suppressed": [], "version_id": "1.17.1"}