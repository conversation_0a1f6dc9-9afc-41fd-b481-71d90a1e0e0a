{".class": "MypyFile", "_fullname": "google.api_core.path_template", "future_import_flags": ["unicode_literals"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "_MULTI_SEGMENT_PATTERN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.api_core.path_template._MULTI_SEGMENT_PATTERN", "name": "_MULTI_SEGMENT_PATTERN", "setter_type": null, "type": "builtins.str"}}, "_SINGLE_SEGMENT_PATTERN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.api_core.path_template._SINGLE_SEGMENT_PATTERN", "name": "_SINGLE_SEGMENT_PATTERN", "setter_type": null, "type": "builtins.str"}}, "_VARIABLE_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.api_core.path_template._VARIABLE_RE", "name": "_VARIABLE_RE", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.path_template.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.path_template.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.path_template.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.path_template.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.path_template.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.path_template.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_expand_variable_match": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["positional_vars", "named_vars", "match"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.path_template._expand_variable_match", "name": "_expand_variable_match", "type": null}}, "_generate_pattern_for_template": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["tmpl"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.path_template._generate_pattern_for_template", "name": "_generate_pattern_for_template", "type": null}}, "_replace_variable_with_pattern": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["match"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.path_template._replace_variable_with_pattern", "name": "_replace_variable_with_pattern", "type": null}}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "delete_field": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["request", "field"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.path_template.delete_field", "name": "delete_field", "type": null}}, "deque": {".class": "SymbolTableNode", "cross_ref": "collections.deque", "kind": "Gdef"}, "expand": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["tmpl", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.path_template.expand", "name": "expand", "type": null}}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "get_field": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["request", "field"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.path_template.get_field", "name": "get_field", "type": null}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "transcode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["http_options", "message", "request_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.path_template.transcode", "name": "transcode", "type": null}}, "unicode_literals": {".class": "SymbolTableNode", "cross_ref": "__future__.unicode_literals", "kind": "Gdef"}, "validate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["tmpl", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.path_template.validate", "name": "validate", "type": null}}}, "path": "/Users/<USER>/.pyenv/versions/3.12/lib/python3.12/site-packages/google/api_core/path_template.py"}