{"data_mtime": 1757043297, "dep_lines": [26, 28, 29, 30, 31, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 10, 5, 30, 30, 30, 30], "dependencies": ["__future__", "collections", "copy", "functools", "re", "builtins", "_frozen_importlib", "abc", "enum", "typing"], "hash": "4d80fac2e2c1a1ec24e7aea2ed0b233f0f97a6d9", "id": "google.api_core.path_template", "ignore_all": true, "interface_hash": "56bbfcf7741ccd1911bee913b4cd12a3a24b3157", "mtime": 1753080525, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/.pyenv/versions/3.12/lib/python3.12/site-packages/google/api_core/path_template.py", "plugin_data": null, "size": 11685, "suppressed": [], "version_id": "1.17.1"}