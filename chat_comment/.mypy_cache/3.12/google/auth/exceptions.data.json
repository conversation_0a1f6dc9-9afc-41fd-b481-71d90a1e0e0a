{".class": "MypyFile", "_fullname": "google.auth.exceptions", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ClientCertError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.auth.exceptions.GoogleAuthError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.auth.exceptions.ClientCertError", "name": "ClientCertError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.auth.exceptions.ClientCertError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.auth.exceptions", "mro": ["google.auth.exceptions.ClientCertError", "google.auth.exceptions.GoogleAuthError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "retryable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.auth.exceptions.ClientCertError.retryable", "name": "retryable", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.auth.exceptions.ClientCertError.retryable", "name": "retryable", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.auth.exceptions.ClientCertError"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "retryable of ClientCertError", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.auth.exceptions.ClientCertError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.auth.exceptions.ClientCertError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DefaultCredentialsError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.auth.exceptions.GoogleAuthError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.auth.exceptions.DefaultCredentialsError", "name": "DefaultCredentialsError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.auth.exceptions.DefaultCredentialsError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.auth.exceptions", "mro": ["google.auth.exceptions.DefaultCredentialsError", "google.auth.exceptions.GoogleAuthError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.auth.exceptions.DefaultCredentialsError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.auth.exceptions.DefaultCredentialsError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GoogleAuthError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.auth.exceptions.GoogleAuthError", "name": "GoogleAuthError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.auth.exceptions.GoogleAuthError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.auth.exceptions", "mro": ["google.auth.exceptions.GoogleAuthError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.auth.exceptions.GoogleAuthError.__init__", "name": "__init__", "type": null}}, "_retryable": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.auth.exceptions.GoogleAuthError._retryable", "name": "_retryable", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "retryable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.auth.exceptions.GoogleAuthError.retryable", "name": "retryable", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.auth.exceptions.GoogleAuthError.retryable", "name": "retryable", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.auth.exceptions.GoogleAuthError"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "retryable of GoogleAuthError", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.auth.exceptions.GoogleAuthError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.auth.exceptions.GoogleAuthError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidOperation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.auth.exceptions.DefaultCredentialsError", "builtins.ValueError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.auth.exceptions.InvalidOperation", "name": "InvalidOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.auth.exceptions.InvalidOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.auth.exceptions", "mro": ["google.auth.exceptions.InvalidOperation", "google.auth.exceptions.DefaultCredentialsError", "google.auth.exceptions.GoogleAuthError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.auth.exceptions.InvalidOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.auth.exceptions.InvalidOperation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidResource": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.auth.exceptions.DefaultCredentialsError", "builtins.ValueError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.auth.exceptions.InvalidResource", "name": "InvalidResource", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.auth.exceptions.InvalidResource", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.auth.exceptions", "mro": ["google.auth.exceptions.InvalidResource", "google.auth.exceptions.DefaultCredentialsError", "google.auth.exceptions.GoogleAuthError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.auth.exceptions.InvalidResource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.auth.exceptions.InvalidResource", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.auth.exceptions.DefaultCredentialsError", "builtins.TypeError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.auth.exceptions.InvalidType", "name": "InvalidType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.auth.exceptions.InvalidType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.auth.exceptions", "mro": ["google.auth.exceptions.InvalidType", "google.auth.exceptions.DefaultCredentialsError", "google.auth.exceptions.GoogleAuthError", "builtins.TypeError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.auth.exceptions.InvalidType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.auth.exceptions.InvalidType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidValue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.auth.exceptions.DefaultCredentialsError", "builtins.ValueError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.auth.exceptions.InvalidValue", "name": "InvalidValue", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.auth.exceptions.InvalidValue", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.auth.exceptions", "mro": ["google.auth.exceptions.InvalidValue", "google.auth.exceptions.DefaultCredentialsError", "google.auth.exceptions.GoogleAuthError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.auth.exceptions.InvalidValue.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.auth.exceptions.InvalidValue", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MalformedError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.auth.exceptions.DefaultCredentialsError", "builtins.ValueError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.auth.exceptions.MalformedError", "name": "MalformedError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.auth.exceptions.MalformedError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.auth.exceptions", "mro": ["google.auth.exceptions.MalformedError", "google.auth.exceptions.DefaultCredentialsError", "google.auth.exceptions.GoogleAuthError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.auth.exceptions.MalformedError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.auth.exceptions.MalformedError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MutualTLSChannelError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.auth.exceptions.GoogleAuthError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.auth.exceptions.MutualTLSChannelError", "name": "MutualTLSChannelError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.auth.exceptions.MutualTLSChannelError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.auth.exceptions", "mro": ["google.auth.exceptions.MutualTLSChannelError", "google.auth.exceptions.GoogleAuthError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.auth.exceptions.MutualTLSChannelError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.auth.exceptions.MutualTLSChannelError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OAuthError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.auth.exceptions.GoogleAuthError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.auth.exceptions.OAuthError", "name": "OAuthError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.auth.exceptions.OAuthError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.auth.exceptions", "mro": ["google.auth.exceptions.OAuthError", "google.auth.exceptions.GoogleAuthError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.auth.exceptions.OAuthError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.auth.exceptions.OAuthError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OSError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.auth.exceptions.DefaultCredentialsError", "builtins.OSError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.auth.exceptions.OSError", "name": "OSError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.auth.exceptions.OSError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.auth.exceptions", "mro": ["google.auth.exceptions.OSError", "google.auth.exceptions.DefaultCredentialsError", "google.auth.exceptions.GoogleAuthError", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.auth.exceptions.OSError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.auth.exceptions.OSError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ReauthFailError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.auth.exceptions.RefreshError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.auth.exceptions.ReauthFailError", "name": "ReauthFailError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.auth.exceptions.ReauthFailError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.auth.exceptions", "mro": ["google.auth.exceptions.ReauthFailError", "google.auth.exceptions.RefreshError", "google.auth.exceptions.GoogleAuthError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "message", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.auth.exceptions.ReauthFailError.__init__", "name": "__init__", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.auth.exceptions.ReauthFailError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.auth.exceptions.ReauthFailError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ReauthSamlChallengeFailError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.auth.exceptions.ReauthFailError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.auth.exceptions.ReauthSamlChallengeFailError", "name": "ReauthSamlChallengeFailError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.auth.exceptions.ReauthSamlChallengeFailError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.auth.exceptions", "mro": ["google.auth.exceptions.ReauthSamlChallengeFailError", "google.auth.exceptions.ReauthFailError", "google.auth.exceptions.RefreshError", "google.auth.exceptions.GoogleAuthError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.auth.exceptions.ReauthSamlChallengeFailError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.auth.exceptions.ReauthSamlChallengeFailError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RefreshError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.auth.exceptions.GoogleAuthError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.auth.exceptions.RefreshError", "name": "RefreshError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.auth.exceptions.RefreshError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.auth.exceptions", "mro": ["google.auth.exceptions.RefreshError", "google.auth.exceptions.GoogleAuthError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.auth.exceptions.RefreshError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.auth.exceptions.RefreshError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TransportError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.auth.exceptions.GoogleAuthError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.auth.exceptions.TransportError", "name": "TransportError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.auth.exceptions.TransportError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.auth.exceptions", "mro": ["google.auth.exceptions.TransportError", "google.auth.exceptions.GoogleAuthError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.auth.exceptions.TransportError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.auth.exceptions.TransportError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UserAccessTokenError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.auth.exceptions.GoogleAuthError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.auth.exceptions.UserAccessTokenError", "name": "UserAccessTokenError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.auth.exceptions.UserAccessTokenError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.auth.exceptions", "mro": ["google.auth.exceptions.UserAccessTokenError", "google.auth.exceptions.GoogleAuthError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.auth.exceptions.UserAccessTokenError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.auth.exceptions.UserAccessTokenError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.auth.exceptions.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.auth.exceptions.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.auth.exceptions.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.auth.exceptions.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.auth.exceptions.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.auth.exceptions.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/Users/<USER>/.pyenv/versions/3.12/lib/python3.12/site-packages/google/auth/exceptions.py"}