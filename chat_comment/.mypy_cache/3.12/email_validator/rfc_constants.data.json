{".class": "MypyFile", "_fullname": "email_validator.rfc_constants", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ATEXT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "email_validator.rfc_constants.ATEXT", "name": "ATEXT", "setter_type": null, "type": "builtins.str"}}, "ATEXT_HOSTNAME_INTL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "email_validator.rfc_constants.ATEXT_HOSTNAME_INTL", "name": "ATEXT_HOSTNAME_INTL", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "ATEXT_INTL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "email_validator.rfc_constants.ATEXT_INTL", "name": "ATEXT_INTL", "setter_type": null, "type": "builtins.str"}}, "ATEXT_INTL_DOT_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "email_validator.rfc_constants.ATEXT_INTL_DOT_RE", "name": "ATEXT_INTL_DOT_RE", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "ATEXT_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "email_validator.rfc_constants.ATEXT_RE", "name": "ATEXT_RE", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "CASE_INSENSITIVE_MAILBOX_NAMES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "email_validator.rfc_constants.CASE_INSENSITIVE_MAILBOX_NAMES", "name": "CASE_INSENSITIVE_MAILBOX_NAMES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "DNS_LABEL_LENGTH_LIMIT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "email_validator.rfc_constants.DNS_LABEL_LENGTH_LIMIT", "name": "DNS_LABEL_LENGTH_LIMIT", "setter_type": null, "type": "builtins.int"}}, "DOMAIN_LITERAL_CHARS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "email_validator.rfc_constants.DOMAIN_LITERAL_CHARS", "name": "DOMAIN_LITERAL_CHARS", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "DOMAIN_MAX_LENGTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "email_validator.rfc_constants.DOMAIN_MAX_LENGTH", "name": "DOMAIN_MAX_LENGTH", "setter_type": null, "type": "builtins.int"}}, "DOMAIN_NAME_REGEX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "email_validator.rfc_constants.DOMAIN_NAME_REGEX", "name": "DOMAIN_NAME_REGEX", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "DOT_ATOM_TEXT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "email_validator.rfc_constants.DOT_ATOM_TEXT", "name": "DOT_ATOM_TEXT", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "DOT_ATOM_TEXT_HOSTNAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "email_validator.rfc_constants.DOT_ATOM_TEXT_HOSTNAME", "name": "DOT_ATOM_TEXT_HOSTNAME", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "DOT_ATOM_TEXT_INTL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "email_validator.rfc_constants.DOT_ATOM_TEXT_INTL", "name": "DOT_ATOM_TEXT_INTL", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "EMAIL_MAX_LENGTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "email_validator.rfc_constants.EMAIL_MAX_LENGTH", "name": "EMAIL_MAX_LENGTH", "setter_type": null, "type": "builtins.int"}}, "HOSTNAME_LABEL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "email_validator.rfc_constants.HOSTNAME_LABEL", "name": "HOSTNAME_LABEL", "setter_type": null, "type": "builtins.str"}}, "LOCAL_PART_MAX_LENGTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "email_validator.rfc_constants.LOCAL_PART_MAX_LENGTH", "name": "LOCAL_PART_MAX_LENGTH", "setter_type": null, "type": "builtins.int"}}, "QTEXT_INTL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "email_validator.rfc_constants.QTEXT_INTL", "name": "QTEXT_INTL", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email_validator.rfc_constants.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email_validator.rfc_constants.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email_validator.rfc_constants.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email_validator.rfc_constants.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email_validator.rfc_constants.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email_validator.rfc_constants.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}}, "path": "/Users/<USER>/.pyenv/versions/3.12/lib/python3.12/site-packages/email_validator/rfc_constants.py"}