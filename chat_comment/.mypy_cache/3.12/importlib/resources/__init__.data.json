{".class": "MypyFile", "_fullname": "importlib.resources", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AbstractContextManager": {".class": "SymbolTableNode", "cross_ref": "contextlib.AbstractContextManager", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Anchor": {".class": "SymbolTableNode", "cross_ref": "importlib.resources._common.Anchor", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BinaryIO": {".class": "SymbolTableNode", "cross_ref": "typing.BinaryIO", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModuleType": {".class": "SymbolTableNode", "cross_ref": "types.ModuleType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Package": {".class": "SymbolTableNode", "cross_ref": "importlib.resources._common.Package", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Resource": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "importlib.resources.Resource", "line": 42, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.str"}}, "ResourceReader": {".class": "SymbolTableNode", "cross_ref": "importlib.resources.abc.ResourceReader", "kind": "Gdef"}, "TextIO": {".class": "SymbolTableNode", "cross_ref": "typing.TextIO", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Traversable": {".class": "SymbolTableNode", "cross_ref": "importlib.resources.abc.Traversable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "importlib.resources.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib.resources.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib.resources.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib.resources.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib.resources.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib.resources.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib.resources.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib.resources.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "as_file": {".class": "SymbolTableNode", "cross_ref": "importlib.resources._common.as_file", "kind": "Gdef"}, "contents": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["package"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "importlib.resources.contents", "name": "contents", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["package"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "importlib.resources._common.Package"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "contents", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "files": {".class": "SymbolTableNode", "cross_ref": "importlib.resources._common.files", "kind": "Gdef"}, "is_resource": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["package", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "importlib.resources.is_resource", "name": "is_resource", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["package", "name"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "importlib.resources._common.Package"}, "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_resource", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "open_binary": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["package", "resource"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "importlib.resources.open_binary", "name": "open_binary", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["package", "resource"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "importlib.resources._common.Package"}, "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "open_binary", "ret_type": "typing.BinaryIO", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "open_text": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["package", "resource", "encoding", "errors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "importlib.resources.open_text", "name": "open_text", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["package", "resource", "encoding", "errors"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "importlib.resources._common.Package"}, "builtins.str", "builtins.str", "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "open_text", "ret_type": "typing.TextIO", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_hidden": true, "module_public": false}, "path": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["package", "resource"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "importlib.resources.path", "name": "path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["package", "resource"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "importlib.resources._common.Package"}, "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "path", "ret_type": {".class": "Instance", "args": ["pathlib.Path", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "extra_attrs": null, "type_ref": "contextlib.AbstractContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_binary": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["package", "resource"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "importlib.resources.read_binary", "name": "read_binary", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["package", "resource"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "importlib.resources._common.Package"}, "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "read_binary", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_text": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["package", "resource", "encoding", "errors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "importlib.resources.read_text", "name": "read_text", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["package", "resource", "encoding", "errors"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "importlib.resources._common.Package"}, "builtins.str", "builtins.str", "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "read_text", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "/Users/<USER>/.pyenv/versions/3.12/lib/python3.12/site-packages/mypy/typeshed/stdlib/importlib/resources/__init__.pyi"}