{".class": "MypyFile", "_fullname": "reports.queries", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "GET_BOTTOM_10_PERFORMING_OFFERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "reports.queries.GET_BOTTOM_10_PERFORMING_OFFERS", "name": "GET_BOTTOM_10_PERFORMING_OFFERS", "setter_type": null, "type": "builtins.str"}}, "GET_MARGIN_AND_REVENUE_GRAPH_DATA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "reports.queries.GET_MARGIN_AND_REVENUE_GRAPH_DATA", "name": "GET_MARGIN_AND_REVENUE_GRAPH_DATA", "setter_type": null, "type": "builtins.str"}}, "GET_POST_OFFER_ANALYSIS_REPORT_DATA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "reports.queries.GET_POST_OFFER_ANALYSIS_REPORT_DATA", "name": "GET_POST_OFFER_ANALYSIS_REPORT_DATA", "setter_type": null, "type": "builtins.str"}}, "GET_POST_OFFER_ANALYSIS_REPORT_DATA_OLD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "reports.queries.GET_POST_OFFER_ANALYSIS_REPORT_DATA_OLD", "name": "GET_POST_OFFER_ANALYSIS_REPORT_DATA_OLD", "setter_type": null, "type": "builtins.str"}}, "GET_PROMOS_BY_FILTERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "reports.queries.GET_PROMOS_BY_FILTERS", "name": "GET_PROMOS_BY_FILTERS", "setter_type": null, "type": "builtins.str"}}, "GET_PROMO_IDS_BY_FILTERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "reports.queries.GET_PROMO_IDS_BY_FILTERS", "name": "GET_PROMO_IDS_BY_FILTERS", "setter_type": null, "type": "builtins.str"}}, "GET_PROMO_IDS_BY_IDS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "reports.queries.GET_PROMO_IDS_BY_IDS", "name": "GET_PROMO_IDS_BY_IDS", "setter_type": null, "type": "builtins.str"}}, "GET_TOP_10_PERFORMING_OFFERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "reports.queries.GET_TOP_10_PERFORMING_OFFERS", "name": "GET_TOP_10_PERFORMING_OFFERS", "setter_type": null, "type": "builtins.str"}}, "GET_TOP_AND_BOTTOM_PERFORMING_OFFERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "reports.queries.GET_TOP_AND_BOTTOM_PERFORMING_OFFERS", "name": "GET_TOP_AND_BOTTOM_PERFORMING_OFFERS", "setter_type": null, "type": "builtins.str"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "reports.queries.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "reports.queries.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "reports.queries.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "reports.queries.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "reports.queries.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "reports.queries.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/Users/<USER>/impact/pricesmart-promo-v3/backend/reports/queries.py"}