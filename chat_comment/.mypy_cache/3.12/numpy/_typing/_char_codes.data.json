{".class": "MypyFile", "_fullname": "numpy._typing._char_codes", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "_BoolCodes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._BoolCodes", "line": 3, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "?"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=?"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<?"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">?"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bool"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bool_"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bool8"}], "uses_pep604_syntax": false}}}, "_ByteCodes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._ByteCodes", "line": 22, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "byte"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "b"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=b"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<b"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">b"}], "uses_pep604_syntax": false}}}, "_BytesCodes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._BytesCodes", "line": 46, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "bytes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bytes_"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bytes0"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "S"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=S"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<S"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">S"}], "uses_pep604_syntax": false}}}, "_CDoubleCodes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._CDoubleCodes", "line": 42, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "cdouble"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "complex"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "complex_"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cfloat"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "D"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=D"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<D"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">D"}], "uses_pep604_syntax": false}}}, "_CLongDoubleCodes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._CLongDoubleCodes", "line": 43, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "clongdo<PERSON>le"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "clongfloat"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "longcomplex"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "G"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=G"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<G"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">G"}], "uses_pep604_syntax": false}}}, "_CSingleCodes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._CSingleCodes", "line": 41, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "csingle"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "singlecomplex"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=F"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<F"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">F"}], "uses_pep604_syntax": false}}}, "_Complex128Codes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._Complex128Codes", "line": 20, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "complex128"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "c16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=c16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<c16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">c16"}], "uses_pep604_syntax": false}}}, "_Complex64Codes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._Complex64Codes", "line": 19, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "complex64"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "c8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=c8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<c8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">c8"}], "uses_pep604_syntax": false}}}, "_DT64Codes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._DT64Codes", "line": 50, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=datetime64"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<datetime64"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">datetime64"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[Y]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=datetime64[Y]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<datetime64[Y]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">datetime64[Y]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[M]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=datetime64[M]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<datetime64[M]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">datetime64[M]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[W]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=datetime64[W]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<datetime64[W]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">datetime64[W]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[D]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=datetime64[D]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<datetime64[D]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">datetime64[D]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[h]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=datetime64[h]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<datetime64[h]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">datetime64[h]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[m]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=datetime64[m]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<datetime64[m]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">datetime64[m]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[s]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=datetime64[s]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<datetime64[s]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">datetime64[s]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[ms]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=datetime64[ms]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<datetime64[ms]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">datetime64[ms]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[us]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=datetime64[us]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<datetime64[us]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">datetime64[us]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[ns]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=datetime64[ns]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<datetime64[ns]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">datetime64[ns]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[ps]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=datetime64[ps]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<datetime64[ps]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">datetime64[ps]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[fs]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=datetime64[fs]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<datetime64[fs]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">datetime64[fs]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[as]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=datetime64[as]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<datetime64[as]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">datetime64[as]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "M"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=M"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "M8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=M8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "M8[Y]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=M8[Y]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[Y]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[Y]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "M8[M]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=M8[M]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[M]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[M]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "M8[W]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=M8[W]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[W]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[W]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "M8[D]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=M8[D]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[D]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[D]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "M8[h]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=M8[h]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[h]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[h]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "M8[m]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=M8[m]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[m]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[m]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "M8[s]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=M8[s]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[s]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[s]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "M8[ms]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=M8[ms]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[ms]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[ms]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "M8[us]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=M8[us]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[us]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[us]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "M8[ns]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=M8[ns]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[ns]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[ns]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "M8[ps]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=M8[ps]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[ps]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[ps]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "M8[fs]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=M8[fs]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[fs]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[fs]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "M8[as]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=M8[as]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[as]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[as]"}], "uses_pep604_syntax": false}}}, "_DoubleCodes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._DoubleCodes", "line": 38, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "double"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "float"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "float_"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "d"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=d"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<d"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">d"}], "uses_pep604_syntax": false}}}, "_Float16Codes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._Float16Codes", "line": 15, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "float16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "f2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=f2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<f2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">f2"}], "uses_pep604_syntax": false}}}, "_Float32Codes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._Float32Codes", "line": 16, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "float32"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "f4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=f4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<f4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">f4"}], "uses_pep604_syntax": false}}}, "_Float64Codes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._Float64Codes", "line": 17, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "float64"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "f8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=f8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<f8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">f8"}], "uses_pep604_syntax": false}}}, "_HalfCodes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._HalfCodes", "line": 36, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "half"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "e"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=e"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<e"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">e"}], "uses_pep604_syntax": false}}}, "_Int16Codes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._Int16Codes", "line": 11, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "int16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "i2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=i2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<i2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">i2"}], "uses_pep604_syntax": false}}}, "_Int32Codes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._Int32Codes", "line": 12, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "int32"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "i4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=i4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<i4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">i4"}], "uses_pep604_syntax": false}}}, "_Int64Codes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._Int64Codes", "line": 13, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "int64"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "i8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=i8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<i8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">i8"}], "uses_pep604_syntax": false}}}, "_Int8Codes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._Int8Codes", "line": 10, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "int8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "i1"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=i1"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<i1"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">i1"}], "uses_pep604_syntax": false}}}, "_IntCCodes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._IntCCodes", "line": 24, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "intc"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "i"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=i"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<i"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">i"}], "uses_pep604_syntax": false}}}, "_IntCodes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._IntCodes", "line": 26, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "long"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "int"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "int_"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "l"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=l"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<l"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">l"}], "uses_pep604_syntax": false}}}, "_IntPCodes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._IntPCodes", "line": 25, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "intp"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "int0"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "p"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=p"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<p"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">p"}], "uses_pep604_syntax": false}}}, "_LongDoubleCodes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._LongDoubleCodes", "line": 39, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "longdouble"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "longfloat"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=g"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<g"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">g"}], "uses_pep604_syntax": false}}}, "_LongLongCodes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._LongLongCodes", "line": 27, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "longlong"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "q"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=q"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<q"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">q"}], "uses_pep604_syntax": false}}}, "_ObjectCodes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._ObjectCodes", "line": 48, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "object"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "object_"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "O"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=O"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<O"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">O"}], "uses_pep604_syntax": false}}}, "_ShortCodes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._ShortCodes", "line": 23, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "short"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "h"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=h"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<h"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">h"}], "uses_pep604_syntax": false}}}, "_SingleCodes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._SingleCodes", "line": 37, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "single"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "f"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=f"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<f"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">f"}], "uses_pep604_syntax": false}}}, "_StrCodes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._StrCodes", "line": 45, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "str"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "str_"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "str0"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "unicode"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "unicode_"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "U"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=U"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<U"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">U"}], "uses_pep604_syntax": false}}}, "_TD64Codes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._TD64Codes", "line": 81, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=timedelta64"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<timedelta64"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">timedelta64"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[Y]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=timedelta64[Y]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<timedelta64[Y]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">timedelta64[Y]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[M]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=timedelta64[M]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<timedelta64[M]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">timedelta64[M]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[W]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=timedelta64[W]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<timedelta64[W]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">timedelta64[W]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[D]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=timedelta64[D]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<timedelta64[D]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">timedelta64[D]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[h]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=timedelta64[h]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<timedelta64[h]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">timedelta64[h]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[m]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=timedelta64[m]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<timedelta64[m]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">timedelta64[m]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[s]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=timedelta64[s]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<timedelta64[s]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">timedelta64[s]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[ms]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=timedelta64[ms]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<timedelta64[ms]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">timedelta64[ms]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[us]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=timedelta64[us]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<timedelta64[us]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">timedelta64[us]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[ns]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=timedelta64[ns]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<timedelta64[ns]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">timedelta64[ns]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[ps]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=timedelta64[ps]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<timedelta64[ps]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">timedelta64[ps]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[fs]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=timedelta64[fs]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<timedelta64[fs]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">timedelta64[fs]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[as]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=timedelta64[as]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<timedelta64[as]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">timedelta64[as]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "m"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=m"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "m8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=m8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "m8[Y]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=m8[Y]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[Y]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[Y]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "m8[M]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=m8[M]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[M]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[M]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "m8[W]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=m8[W]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[W]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[W]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "m8[D]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=m8[D]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[D]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[D]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "m8[h]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=m8[h]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[h]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[h]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "m8[m]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=m8[m]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[m]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[m]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "m8[s]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=m8[s]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[s]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[s]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "m8[ms]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=m8[ms]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[ms]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[ms]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "m8[us]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=m8[us]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[us]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[us]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "m8[ns]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=m8[ns]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[ns]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[ns]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "m8[ps]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=m8[ps]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[ps]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[ps]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "m8[fs]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=m8[fs]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[fs]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[fs]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "m8[as]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=m8[as]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[as]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[as]"}], "uses_pep604_syntax": false}}}, "_UByteCodes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._UByteCodes", "line": 29, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "ubyte"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "B"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=B"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<B"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">B"}], "uses_pep604_syntax": false}}}, "_UInt16Codes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._UInt16Codes", "line": 6, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "uint16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "u2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=u2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<u2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">u2"}], "uses_pep604_syntax": false}}}, "_UInt32Codes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._UInt32Codes", "line": 7, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "uint32"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "u4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=u4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<u4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">u4"}], "uses_pep604_syntax": false}}}, "_UInt64Codes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._UInt64Codes", "line": 8, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "uint64"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "u8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=u8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<u8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">u8"}], "uses_pep604_syntax": false}}}, "_UInt8Codes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._UInt8Codes", "line": 5, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "uint8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "u1"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=u1"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<u1"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">u1"}], "uses_pep604_syntax": false}}}, "_UIntCCodes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._UIntCCodes", "line": 31, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "uintc"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "I"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=I"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<I"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">I"}], "uses_pep604_syntax": false}}}, "_UIntCodes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._UIntCodes", "line": 33, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<PERSON><PERSON>"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "uint"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "L"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=L"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<L"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">L"}], "uses_pep604_syntax": false}}}, "_UIntPCodes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._UIntPCodes", "line": 32, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "uintp"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "uint0"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "P"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=P"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<P"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">P"}], "uses_pep604_syntax": false}}}, "_ULongLongCodes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._ULongLongCodes", "line": 34, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<PERSON><PERSON><PERSON>"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "Q"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=Q"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<Q"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">Q"}], "uses_pep604_syntax": false}}}, "_UShortCodes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._UShortCodes", "line": 30, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "ushort"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "H"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=H"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<H"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">H"}], "uses_pep604_syntax": false}}}, "_VoidCodes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._char_codes._VoidCodes", "line": 47, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "void"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "void0"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "V"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "=V"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<V"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">V"}], "uses_pep604_syntax": false}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._char_codes.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._char_codes.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._char_codes.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._char_codes.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._char_codes.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._char_codes.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/Users/<USER>/.pyenv/versions/3.12/lib/python3.12/site-packages/numpy/_typing/_char_codes.py"}