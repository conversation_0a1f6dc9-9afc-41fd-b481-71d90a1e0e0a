{"data_mtime": 1757043297, "dep_lines": [11, 1, 1, 1, 1], "dep_prios": [5, 5, 30, 30, 30], "dependencies": ["numpy._utils._convertions", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "46bdfc49f6b02ed490c3efbee20589d8979b6d99", "id": "numpy._utils", "ignore_all": true, "interface_hash": "899e491c21e1278c4944db49363fa2514ff6ae61", "mtime": 1752847426, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/.pyenv/versions/3.12/lib/python3.12/site-packages/numpy/_utils/__init__.py", "plugin_data": null, "size": 723, "suppressed": [], "version_id": "1.17.1"}