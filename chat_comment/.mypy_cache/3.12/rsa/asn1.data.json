{".class": "MypyFile", "_fullname": "rsa.asn1", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsnPubKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rsa.asn1.AsnP<PERSON><PERSON>ey", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "rsa.asn1.AsnP<PERSON><PERSON>ey", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rsa.asn1", "mro": ["rsa.asn1.AsnP<PERSON><PERSON>ey", "builtins.object"], "names": {".class": "SymbolTable", "componentType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "rsa.asn1.AsnPubKey.componentType", "name": "componentType", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "rsa.asn1.namedtype", "source_any": {".class": "AnyType", "missing_import_name": "rsa.asn1.namedtype", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rsa.asn1.AsnPubKey.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rsa.asn1.AsnP<PERSON><PERSON>ey", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OpenSSLPubKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rsa.asn1.OpenSSLPubKey", "name": "OpenSSLPub<PERSON>ey", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "rsa.asn1.OpenSSLPubKey", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rsa.asn1", "mro": ["rsa.asn1.OpenSSLPubKey", "builtins.object"], "names": {".class": "SymbolTable", "componentType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "rsa.asn1.OpenSSLPubKey.componentType", "name": "componentType", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "rsa.asn1.namedtype", "source_any": {".class": "AnyType", "missing_import_name": "rsa.asn1.namedtype", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rsa.asn1.OpenSSLPubKey.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rsa.asn1.OpenSSLPubKey", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PubKeyHeader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rsa.asn1.Pub<PERSON>eyHeader", "name": "PubKeyHeader", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "rsa.asn1.Pub<PERSON>eyHeader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rsa.asn1", "mro": ["rsa.asn1.Pub<PERSON>eyHeader", "builtins.object"], "names": {".class": "SymbolTable", "componentType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "rsa.asn1.PubKeyHeader.componentType", "name": "componentType", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "rsa.asn1.namedtype", "source_any": {".class": "AnyType", "missing_import_name": "rsa.asn1.namedtype", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rsa.asn1.PubKeyHeader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rsa.asn1.Pub<PERSON>eyHeader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rsa.asn1.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rsa.asn1.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rsa.asn1.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rsa.asn1.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rsa.asn1.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rsa.asn1.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "namedtype": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "rsa.asn1.namedtype", "name": "namedtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "rsa.asn1.namedtype", "source_any": null, "type_of_any": 3}}}, "tag": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "rsa.asn1.tag", "name": "tag", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "rsa.asn1.tag", "source_any": null, "type_of_any": 3}}}, "univ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "rsa.asn1.univ", "name": "univ", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "rsa.asn1.univ", "source_any": null, "type_of_any": 3}}}}, "path": "/Users/<USER>/.pyenv/versions/3.12/lib/python3.12/site-packages/rsa/asn1.py"}