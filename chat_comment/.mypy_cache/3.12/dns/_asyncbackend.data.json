{".class": "MypyFile", "_fullname": "dns._asyncbackend", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Backend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns._asyncbackend.Backend", "name": "Backend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns._asyncbackend.Backend", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns._asyncbackend", "mro": ["dns._asyncbackend.Backend", "builtins.object"], "names": {".class": "SymbolTable", "datagram_connection_required": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns._asyncbackend.Backend.datagram_connection_required", "name": "datagram_connection_required", "type": null}}, "get_transport_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns._asyncbackend.Backend.get_transport_class", "name": "get_transport_class", "type": null}}, "make_socket": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "af", "socktype", "proto", "source", "destination", "timeout", "ssl_context", "server_hostname"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns._asyncbackend.Backend.make_socket", "name": "make_socket", "type": null}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns._asyncbackend.Backend.name", "name": "name", "type": null}}, "sleep": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "interval"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns._asyncbackend.Backend.sleep", "name": "sleep", "type": null}}, "wait_for": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "awaitable", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns._asyncbackend.Backend.wait_for", "name": "wait_for", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns._asyncbackend.Backend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns._asyncbackend.Backend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DatagramSocket": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns._asyncbackend.Socket"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns._asyncbackend.DatagramSocket", "name": "DatagramSocket", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns._asyncbackend.DatagramSocket", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns._asyncbackend", "mro": ["dns._asyncbackend.DatagramSocket", "dns._asyncbackend.Socket", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "family"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns._asyncbackend.DatagramSocket.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "family"], "arg_types": ["dns._asyncbackend.DatagramSocket", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of DatagramSocket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "family": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns._asyncbackend.DatagramSocket.family", "name": "family", "setter_type": null, "type": "builtins.int"}}, "recvfrom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "size", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns._asyncbackend.DatagramSocket.recvfrom", "name": "recvfrom", "type": null}}, "sendto": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "what", "destination", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns._asyncbackend.DatagramSocket.sendto", "name": "sendto", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns._asyncbackend.DatagramSocket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns._asyncbackend.DatagramSocket", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NullContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns._asyncbackend.NullContext", "name": "NullContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns._asyncbackend.NullContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns._asyncbackend", "mro": ["dns._asyncbackend.NullContext", "builtins.object"], "names": {".class": "SymbolTable", "__aenter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns._asyncbackend.NullContext.__aenter__", "name": "__aenter__", "type": null}}, "__aexit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc_value", "traceback"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns._asyncbackend.NullContext.__aexit__", "name": "__aexit__", "type": null}}, "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns._asyncbackend.NullContext.__enter__", "name": "__enter__", "type": null}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns._asyncbackend.NullContext.__exit__", "name": "__exit__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "enter_result"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns._asyncbackend.NullContext.__init__", "name": "__init__", "type": null}}, "enter_result": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns._asyncbackend.NullContext.enter_result", "name": "enter_result", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns._asyncbackend.NullContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns._asyncbackend.NullContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NullTransport": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns._asyncbackend.NullTransport", "name": "NullTransport", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns._asyncbackend.NullTransport", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns._asyncbackend", "mro": ["dns._asyncbackend.NullTransport", "builtins.object"], "names": {".class": "SymbolTable", "connect_tcp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "host", "port", "timeout", "local_address"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns._asyncbackend.NullTransport.connect_tcp", "name": "connect_tcp", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns._asyncbackend.NullTransport.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns._asyncbackend.NullTransport", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Socket": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns._asyncbackend.Socket", "name": "Socket", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns._asyncbackend.Socket", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns._asyncbackend", "mro": ["dns._asyncbackend.Socket", "builtins.object"], "names": {".class": "SymbolTable", "__aenter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns._asyncbackend.Socket.__aenter__", "name": "__aenter__", "type": null}}, "__aexit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc_value", "traceback"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns._asyncbackend.Socket.__aexit__", "name": "__aexit__", "type": null}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns._asyncbackend.Socket.close", "name": "close", "type": null}}, "getpeercert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns._asyncbackend.Socket.getpeercert", "name": "getpeercert", "type": null}}, "getpeername": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns._asyncbackend.Socket.getpeername", "name": "getpeername", "type": null}}, "getsockname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns._asyncbackend.Socket.getsockname", "name": "getsockname", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns._asyncbackend.Socket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns._asyncbackend.Socket", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StreamSocket": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns._asyncbackend.Socket"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns._asyncbackend.StreamSocket", "name": "StreamSocket", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns._asyncbackend.StreamSocket", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns._asyncbackend", "mro": ["dns._asyncbackend.StreamSocket", "dns._asyncbackend.Socket", "builtins.object"], "names": {".class": "SymbolTable", "recv": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "size", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns._asyncbackend.StreamSocket.recv", "name": "recv", "type": null}}, "sendall": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "what", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns._asyncbackend.StreamSocket.sendall", "name": "sendall", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns._asyncbackend.StreamSocket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns._asyncbackend.StreamSocket", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns._asyncbackend.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns._asyncbackend.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns._asyncbackend.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns._asyncbackend.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns._asyncbackend.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns._asyncbackend.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/Users/<USER>/.pyenv/versions/3.12/lib/python3.12/site-packages/dns/_asyncbackend.py"}