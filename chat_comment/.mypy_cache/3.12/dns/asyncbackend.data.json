{".class": "MypyFile", "_fullname": "dns.asyncbackend", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncLibraryNotFoundError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.exception.DNSException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.asyncbackend.AsyncLibraryNotFoundError", "name": "AsyncLibraryNotFoundError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.asyncbackend.AsyncLibraryNotFoundError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.asyncbackend", "mro": ["dns.asyncbackend.AsyncLibraryNotFoundError", "dns.exception.DNSException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.asyncbackend.AsyncLibraryNotFoundError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.asyncbackend.AsyncLibraryNotFoundError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Backend": {".class": "SymbolTableNode", "cross_ref": "dns._asyncbackend.Backend", "kind": "Gdef"}, "DatagramSocket": {".class": "SymbolTableNode", "cross_ref": "dns._asyncbackend.DatagramSocket", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Socket": {".class": "SymbolTableNode", "cross_ref": "dns._asyncbackend.Socket", "kind": "Gdef"}, "StreamSocket": {".class": "SymbolTableNode", "cross_ref": "dns._asyncbackend.StreamSocket", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.asyncbackend.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.asyncbackend.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.asyncbackend.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.asyncbackend.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.asyncbackend.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.asyncbackend.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_backends": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "dns.asyncbackend._backends", "name": "_backends", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "dns._asyncbackend.Backend"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_default_backend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "dns.asyncbackend._default_backend", "name": "_default_backend", "setter_type": null, "type": {".class": "NoneType"}}}, "_no_sniffio": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "dns.asyncbackend._no_sniffio", "name": "_no_sniffio", "setter_type": null, "type": "builtins.bool"}}, "dns": {".class": "SymbolTableNode", "cross_ref": "dns", "kind": "Gdef"}, "get_backend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.asyncbackend.get_backend", "name": "get_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_backend", "ret_type": "dns._asyncbackend.Backend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_default_backend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.asyncbackend.get_default_backend", "name": "get_default_backend", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_default_backend", "ret_type": "dns._asyncbackend.Backend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_default_backend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.asyncbackend.set_default_backend", "name": "set_default_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_default_backend", "ret_type": "dns._asyncbackend.Backend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sniff": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.asyncbackend.sniff", "name": "sniff", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sniff", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/Users/<USER>/.pyenv/versions/3.12/lib/python3.12/site-packages/dns/asyncbackend.py"}