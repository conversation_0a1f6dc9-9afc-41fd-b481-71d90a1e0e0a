{"data_mtime": 1757043296, "dep_lines": [1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 30, 30, 30, 30, 30, 30], "dependencies": ["builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "types", "typing"], "hash": "eb8bbf6df6b39672595a6485b7bd21b4637968e2", "id": "dns.serial", "ignore_all": true, "interface_hash": "4513e8e3932bedec03c17caf51a113735d5780de", "mtime": 1753080526, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/.pyenv/versions/3.12/lib/python3.12/site-packages/dns/serial.py", "plugin_data": null, "size": 3606, "suppressed": [], "version_id": "1.17.1"}