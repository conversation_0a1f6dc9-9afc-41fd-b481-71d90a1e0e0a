{"data_mtime": 1757043297, "dep_lines": [22, 23, 20, 22, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 20, 5, 30, 30, 30], "dependencies": ["dns.enum", "dns.exception", "typing", "dns", "builtins", "_frozen_importlib", "abc", "enum"], "hash": "dcafd17232df9a5864e0231f11bf1977a994f80e", "id": "dns.rdatatype", "ignore_all": true, "interface_hash": "9c910db1e5cc2a2c2ad2aee667acbaafc8fcba41", "mtime": 1753080526, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/.pyenv/versions/3.12/lib/python3.12/site-packages/dns/rdatatype.py", "plugin_data": null, "size": 7339, "suppressed": [], "version_id": "1.17.1"}