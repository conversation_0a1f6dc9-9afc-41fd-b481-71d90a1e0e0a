{".class": "MypyFile", "_fullname": "promotions.queries", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AGGR_SIM_RESULTS_DETAILS_DOWNLOADS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.AGGR_SIM_RESULTS_DETAILS_DOWNLOADS_QUERY", "name": "AGGR_SIM_RESULTS_DETAILS_DOWNLOADS_QUERY", "setter_type": null, "type": "builtins.str"}}, "APPROVE_IA_SCENARIO_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.APPROVE_IA_SCENARIO_QUERY", "name": "APPROVE_IA_SCENARIO_QUERY", "setter_type": null, "type": "builtins.str"}}, "APPROVE_SCENARIO_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.APPROVE_SCENARIO_QUERY", "name": "APPROVE_SCENARIO_QUERY", "setter_type": null, "type": "builtins.str"}}, "BASE_DETAILED_SIMULATION_RESULTS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.BASE_DETAILED_SIMULATION_RESULTS_QUERY", "name": "BASE_DETAILED_SIMULATION_RESULTS_QUERY", "setter_type": null, "type": "builtins.str"}}, "BASE_OVERALL_SIM_RESULTS_DEATILS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.BASE_OVERALL_SIM_RESULTS_DEATILS_QUERY", "name": "BASE_OVERALL_SIM_RESULTS_DEATILS_QUERY", "setter_type": null, "type": "builtins.str"}}, "BULK_EDIT_DISCOUNTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.BULK_EDIT_DISCOUNTS", "name": "BULK_EDIT_DISCOUNTS", "setter_type": null, "type": "builtins.str"}}, "CHECK_EXMD_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.CHECK_EXMD_QUERY", "name": "CHECK_EXMD_QUERY", "setter_type": null, "type": "builtins.str"}}, "CHECK_IF_STEP3_IS_DISABLED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.CHECK_IF_STEP3_IS_DISABLED", "name": "CHECK_IF_STEP3_IS_DISABLED", "setter_type": null, "type": "builtins.str"}}, "CHECK_PROMO_BACKGROUND_PROCESS_STATUS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.CHECK_PROMO_BACKGROUND_PROCESS_STATUS_QUERY", "name": "CHECK_PROMO_<PERSON>C<PERSON>GROUND_PROCESS_STATUS_QUERY", "setter_type": null, "type": "builtins.str"}}, "CHECK_PROMO_EXISTENCE_BY_NAME_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.CHECK_PROMO_EXISTENCE_BY_NAME_QUERY", "name": "CHECK_PROMO_EXISTENCE_BY_NAME_QUERY", "setter_type": null, "type": "builtins.str"}}, "CHECK_UPTO_DISCOUNT_OFFERTYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.CHECK_UPTO_DISCOUNT_OFFERTYPE", "name": "CHECK_UPTO_DISCOUNT_OFFERTYPE", "setter_type": null, "type": "builtins.str"}}, "COPY_DISCOUNTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.COPY_DISCOUNTS", "name": "COPY_DISCOUNTS", "setter_type": null, "type": "builtins.str"}}, "COPY_PROMO_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.COPY_PROMO_QUERY", "name": "COPY_PROMO_QUERY", "setter_type": null, "type": "builtins.str"}}, "CREATE_NEW_SCENARIOS_DATA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.CREATE_NEW_SCENARIOS_DATA", "name": "CREATE_NEW_SCENARIOS_DATA", "setter_type": null, "type": "builtins.str"}}, "CREATE_PLACEHOLDER_PROMO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.CREATE_PLACEHOLDER_PROMO", "name": "CREATE_PLACEHOLDER_PROMO", "setter_type": null, "type": "builtins.str"}}, "CREATE_PROMO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.CREATE_PROMO", "name": "CREATE_PROMO", "setter_type": null, "type": "builtins.str"}}, "CREATE_TIER_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.CREATE_TIER_QUERY", "name": "CREATE_TIER_QUERY", "setter_type": null, "type": "builtins.str"}}, "DELETE_PROMOS_AND_ITS_METRICS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.DELETE_PROMOS_AND_ITS_METRICS_QUERY", "name": "DELETE_PROMOS_AND_ITS_METRICS_QUERY", "setter_type": null, "type": "builtins.str"}}, "DELETE_TIER_INFO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.DELETE_TIER_INFO", "name": "DELETE_TIER_INFO", "setter_type": null, "type": "builtins.str"}}, "EDIT_PROMOS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.EDIT_PROMOS_QUERY", "name": "EDIT_PROMOS_QUERY", "setter_type": null, "type": "builtins.str"}}, "EDIT_TIER_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.EDIT_TIER_QUERY", "name": "EDIT_TIER_QUERY", "setter_type": null, "type": "builtins.str"}}, "EXCLUSION_CHECK_HIERARCHY_VALIDITY_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.EXCLUSION_CHECK_HIERARCHY_VALIDITY_QUERY", "name": "EXCLUSION_CHECK_HIERARCHY_VALIDITY_QUERY", "setter_type": null, "type": "builtins.str"}}, "EXCLUSION_CHECK_VALID_SKU_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.EXCLUSION_CHECK_VALID_SKU_QUERY", "name": "EXCLUSION_CHECK_VALID_SKU_QUERY", "setter_type": null, "type": "builtins.str"}}, "EXECUTION_APPROVED_PROMO_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.EXECUTION_APPROVED_PROMO_QUERY", "name": "EXECUTION_APPROVED_PROMO_QUERY", "setter_type": null, "type": "builtins.str"}}, "FETCH_BMSM_OFFER_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_BMSM_OFFER_TYPES", "name": "FETCH_BMSM_OFFER_TYPES", "setter_type": null, "type": "builtins.str"}}, "FETCH_DD_TILES_FILTER_BASED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_DD_TILES_FILTER_BASED", "name": "FETCH_DD_TILES_FILTER_BASED", "setter_type": null, "type": "builtins.str"}}, "FETCH_DD_TILES_ID_BASED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_DD_TILES_ID_BASED", "name": "FETCH_DD_TILES_ID_BASED", "setter_type": null, "type": "builtins.str"}}, "FETCH_DETAILED_SIMULATION_RESULTS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_DETAILED_SIMULATION_RESULTS_QUERY", "name": "FETCH_DETAILED_SIMULATION_RESULTS_QUERY", "setter_type": null, "type": "builtins.str"}}, "FETCH_DISCOUNTING_LEVEL_VALUES_OVERALL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_DISCOUNTING_LEVEL_VALUES_OVERALL", "name": "FETCH_DISCOUNTING_LEVEL_VALUES_OVERALL", "setter_type": null, "type": "builtins.str"}}, "FETCH_DISCOUNTING_LEVEL_VALUES_PRODUCT_GROUP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_DISCOUNTING_LEVEL_VALUES_PRODUCT_GROUP", "name": "FETCH_DISCOUNTING_LEVEL_VALUES_PRODUCT_GROUP", "setter_type": null, "type": "builtins.str"}}, "FETCH_DISCOUNTING_LEVEL_VALUES_SPECIFIC_PRODUCTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_DISCOUNTING_LEVEL_VALUES_SPECIFIC_PRODUCTS", "name": "FETCH_DISCOUNTING_LEVEL_VALUES_SPECIFIC_PRODUCTS", "setter_type": null, "type": "builtins.str"}}, "FETCH_DISCOUNT_LEVELS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_DISCOUNT_LEVELS", "name": "FETCH_DISCOUNT_LEVELS", "setter_type": null, "type": "builtins.str"}}, "FETCH_DISCOUNT_RULES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_DISCOUNT_RULES", "name": "FETCH_DISCOUNT_RULES", "setter_type": null, "type": "builtins.str"}}, "FETCH_ELIGIBLE_PROMO_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_ELIGIBLE_PROMO_QUERY", "name": "FETCH_ELIGIBLE_PROMO_QUERY", "setter_type": null, "type": "builtins.str"}}, "FETCH_EXMD_PRICE_FILTER_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_EXMD_PRICE_FILTER_QUERY", "name": "FETCH_EXMD_PRICE_FILTER_QUERY", "setter_type": null, "type": "builtins.str"}}, "FETCH_EXMD_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_EXMD_QUERY", "name": "FETCH_EXMD_QUERY", "setter_type": null, "type": "builtins.str"}}, "FETCH_EXMD_SFCC_ATS_CHECK_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_EXMD_SFCC_ATS_CHECK_QUERY", "name": "FETCH_EXMD_SFCC_ATS_CHECK_QUERY", "setter_type": null, "type": "builtins.str"}}, "FETCH_EXMD_SFCC_DROPSHIP_OPTIONS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_EXMD_SFCC_DROPSHIP_OPTIONS_QUERY", "name": "FETCH_EXMD_SFCC_DROPSHIP_OPTIONS_QUERY", "setter_type": null, "type": "builtins.str"}}, "FETCH_EXMD_TARGET_FOLDER_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_EXMD_TARGET_FOLDER_QUERY", "name": "FETCH_EXMD_TARGET_FOLDER_QUERY", "setter_type": null, "type": "builtins.str"}}, "FETCH_EXMD_TEMPLATE_ID_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_EXMD_TEMPLATE_ID_QUERY", "name": "FETCH_EXMD_TEMPLATE_ID_QUERY", "setter_type": null, "type": "builtins.str"}}, "FETCH_LY_TARGETS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_LY_TARGETS_QUERY", "name": "FETCH_LY_TARGETS_QUERY", "setter_type": null, "type": "builtins.str"}}, "FETCH_OPTIMISE_VALID_OFFER_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_OPTIMISE_VALID_OFFER_TYPES", "name": "FETCH_OPTIMISE_VALID_OFFER_TYPES", "setter_type": null, "type": "builtins.str"}}, "FETCH_OVERALL_PRODUCT_AND_STORE_COUNT_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_OVERALL_PRODUCT_AND_STORE_COUNT_QUERY", "name": "FETCH_OVERALL_PRODUCT_AND_STORE_COUNT_QUERY", "setter_type": null, "type": "builtins.str"}}, "FETCH_OVERALL_SIM_RESULTS_DEATILS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_OVERALL_SIM_RESULTS_DEATILS_QUERY", "name": "FETCH_OVERALL_SIM_RESULTS_DEATILS_QUERY", "setter_type": null, "type": "builtins.str"}}, "FETCH_OVERRIDE_REASON": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_OVERRIDE_REASON", "name": "FETCH_OVERRIDE_REASON", "setter_type": null, "type": "builtins.str"}}, "FETCH_PREVIOUSLY_SYNCED_PROMO_ID_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_PREVIOUSLY_SYNCED_PROMO_ID_QUERY", "name": "FETCH_PREVIOUSLY_SYNCED_PROMO_ID_QUERY", "setter_type": null, "type": "builtins.str"}}, "FETCH_PRIORITY_NUMBER_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_PRIORITY_NUMBER_QUERY", "name": "FETCH_PRIORITY_NUMBER_QUERY", "setter_type": null, "type": "builtins.str"}}, "FETCH_PRODUCT_AND_STORE_DISCOUNT_LEVEL_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_PRODUCT_AND_STORE_DISCOUNT_LEVEL_QUERY", "name": "FETCH_PRODUCT_AND_STORE_DISCOUNT_LEVEL_QUERY", "setter_type": null, "type": "builtins.str"}}, "FETCH_PRODUCT_COUNT_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_PRODUCT_COUNT_QUERY", "name": "FETCH_PRODUCT_COUNT_QUERY", "setter_type": null, "type": "builtins.str"}}, "FETCH_PROMOS_CALENDAR_VIEW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_PROMOS_CALENDAR_VIEW", "name": "FETCH_PROMOS_CALENDAR_VIEW", "setter_type": null, "type": "builtins.str"}}, "FETCH_PROMOS_CALENDAR_VIEW_DOWNLOAD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_PROMOS_CALENDAR_VIEW_DOWNLOAD", "name": "FETCH_PROMOS_CALENDAR_VIEW_DOWNLOAD", "setter_type": null, "type": "builtins.str"}}, "FETCH_PROMOS_DECISION_DASHBOARD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_PROMOS_DECISION_DASHBOARD", "name": "FETCH_PROMOS_DECISION_DASHBOARD", "setter_type": null, "type": "builtins.str"}}, "FETCH_PROMOS_DECISION_DASHBOARD_DOWNLOAD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_PROMOS_DECISION_DASHBOARD_DOWNLOAD", "name": "FETCH_PROMOS_DECISION_DASHBOARD_DOWNLOAD", "setter_type": null, "type": "builtins.str"}}, "FETCH_PROMOS_WORKBENCH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_PROMOS_WORKBENCH", "name": "FETCH_PROMOS_WORKBENCH", "setter_type": null, "type": "builtins.str"}}, "FETCH_PROMOS_WORKBENCH_BY_PROMO_IDS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_PROMOS_WORKBENCH_BY_PROMO_IDS", "name": "FETCH_PROMOS_WORKBENCH_BY_PROMO_IDS", "setter_type": null, "type": "builtins.str"}}, "FETCH_PROMOS_WORKBENCH_DOWNLOAD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_PROMOS_WORKBENCH_DOWNLOAD", "name": "FETCH_PROMOS_WORKBENCH_DOWNLOAD", "setter_type": null, "type": "builtins.str"}}, "FETCH_PROMO_PRICE_FILE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_PROMO_PRICE_FILE", "name": "FETCH_PROMO_PRICE_FILE", "setter_type": null, "type": "builtins.str"}}, "FETCH_RESIMULATE_VALID_OFFER_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_RESIMULATE_VALID_OFFER_TYPES", "name": "FETCH_RESIMULATE_VALID_OFFER_TYPES", "setter_type": null, "type": "builtins.str"}}, "FETCH_SCENARIO_COUNT_FOR_GIF_DATA_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_SCENARIO_COUNT_FOR_GIF_DATA_QUERY", "name": "FETCH_SCENARIO_COUNT_FOR_GIF_DATA_QUERY", "setter_type": null, "type": "builtins.str"}}, "FETCH_SCENARIO_ID_DATA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_SCENARIO_ID_DATA", "name": "FETCH_SCENARIO_ID_DATA", "setter_type": null, "type": "builtins.str"}}, "FETCH_STEP0_BASICS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_STEP0_BASICS_QUERY", "name": "FETCH_STEP0_BASICS_QUERY", "setter_type": null, "type": "builtins.str"}}, "FETCH_STEP1_BASICS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_STEP1_BASICS_QUERY", "name": "FETCH_STEP1_BASICS_QUERY", "setter_type": null, "type": "builtins.str"}}, "FETCH_STEP1_DETAILS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_STEP1_DETAILS_QUERY", "name": "FETCH_STEP1_DETAILS_QUERY", "setter_type": null, "type": "builtins.str"}}, "FETCH_STEP1_EXCLUSIONS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_STEP1_EXCLUSIONS_QUERY", "name": "FETCH_STEP1_EXCLUSIONS_QUERY", "setter_type": null, "type": "builtins.str"}}, "FETCH_STEP2_BASICS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_STEP2_BASICS_QUERY", "name": "FETCH_STEP2_BASICS_QUERY", "setter_type": null, "type": "builtins.str"}}, "FETCH_STEP3_BASICS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_STEP3_BASICS_QUERY", "name": "FETCH_STEP3_BASICS_QUERY", "setter_type": null, "type": "builtins.str"}}, "FETCH_STEP3_OPTIMISE_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_STEP3_OPTIMISE_QUERY", "name": "FETCH_STEP3_OPTIMISE_QUERY", "setter_type": null, "type": "builtins.str"}}, "FETCH_STEP3_SIMULATION_RESULTS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_STEP3_SIMULATION_RESULTS_QUERY", "name": "FETCH_STEP3_SIMULATION_RESULTS_QUERY", "setter_type": null, "type": "builtins.str"}}, "FETCH_STORE_COUNT_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_STORE_COUNT_QUERY", "name": "FETCH_STORE_COUNT_QUERY", "setter_type": null, "type": "builtins.str"}}, "FETCH_TIERS_INFO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_TIERS_INFO", "name": "FETCH_TIERS_INFO", "setter_type": null, "type": "builtins.str"}}, "FETCH_TIER_VALID_OFFER_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_TIER_VALID_OFFER_TYPES", "name": "FETCH_TIER_VALID_OFFER_TYPES", "setter_type": null, "type": "builtins.str"}}, "FETCH_VALID_DISCOUNTING_LEVELS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_VALID_DISCOUNTING_LEVELS", "name": "FETCH_VALID_DISCOUNTING_LEVELS", "setter_type": null, "type": "builtins.str"}}, "FETCH_VALID_OFFER_TYPES_BY_PRIORITY_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_VALID_OFFER_TYPES_BY_PRIORITY_QUERY", "name": "FETCH_VALID_OFFER_TYPES_BY_PRIORITY_QUERY", "setter_type": null, "type": "builtins.str"}}, "FETCH_WBP_WCP_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_WBP_WCP_QUERY", "name": "FETCH_WBP_WCP_QUERY", "setter_type": null, "type": "builtins.str"}}, "FETCH_WORKBENCH_TILES_FILTER_BASED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_WORKBENCH_TILES_FILTER_BASED", "name": "FETCH_WORKBENCH_TILES_FILTER_BASED", "setter_type": null, "type": "builtins.str"}}, "FETCH_WORKBENCH_TILES_ID_BASED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FETCH_WORKBENCH_TILES_ID_BASED", "name": "FETCH_WORKBENCH_TILES_ID_BASED", "setter_type": null, "type": "builtins.str"}}, "FILTERED_PROMOTIONS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.FILTERED_PROMOTIONS", "name": "FILTERED_PROMOTIONS", "setter_type": null, "type": "builtins.str"}}, "GET_CONFLICTED_PROMOS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.GET_CONFLICTED_PROMOS", "name": "GET_CONFLICTED_PROMOS", "setter_type": null, "type": "builtins.str"}}, "GET_EXPECTED_TIME_FOR_OPTIMIZATION_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.GET_EXPECTED_TIME_FOR_OPTIMIZATION_QUERY", "name": "GET_EXPECTED_TIME_FOR_OPTIMIZATION_QUERY", "setter_type": null, "type": "builtins.str"}}, "GET_EXPECTED_TIME_FOR_SIMULATION_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.GET_EXPECTED_TIME_FOR_SIMULATION_QUERY", "name": "GET_EXPECTED_TIME_FOR_SIMULATION_QUERY", "setter_type": null, "type": "builtins.str"}}, "GET_NEW_SCENARIO_ID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.GET_NEW_SCENARIO_ID", "name": "GET_NEW_SCENARIO_ID", "setter_type": null, "type": "builtins.str"}}, "GET_NON_PAST_PROMOS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.GET_NON_PAST_PROMOS_QUERY", "name": "GET_NON_PAST_PROMOS_QUERY", "setter_type": null, "type": "builtins.str"}}, "GET_OVERRIDE_FORECAST_FOR_A_PROMO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.GET_OVERRIDE_FORECAST_FOR_A_PROMO", "name": "GET_OVERRIDE_FORECAST_FOR_A_PROMO", "setter_type": null, "type": "builtins.str"}}, "GET_PRODUCT_DETAILS_OF_PROMO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.GET_PRODUCT_DETAILS_OF_PROMO", "name": "GET_PRODUCT_DETAILS_OF_PROMO", "setter_type": null, "type": "builtins.str"}}, "GET_PROMO_DISCOUNTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.GET_PROMO_DISCOUNTS", "name": "GET_PROMO_DISCOUNTS", "setter_type": null, "type": "builtins.str"}}, "GET_PROMO_DISCOUNTS_TABLE_METADATA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.GET_PROMO_DISCOUNTS_TABLE_METADATA", "name": "GET_PROMO_DISCOUNTS_TABLE_METADATA", "setter_type": null, "type": "builtins.str"}}, "GET_PROMO_OFFER_TYPE_DETAILS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.GET_PROMO_OFFER_TYPE_DETAILS", "name": "GET_PROMO_OFFER_TYPE_DETAILS", "setter_type": null, "type": "builtins.str"}}, "GET_PROMO_SCENARIO_DEATILS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.GET_PROMO_SCENARIO_DEATILS_QUERY", "name": "GET_PROMO_SCENARIO_DEATILS_QUERY", "setter_type": null, "type": "builtins.str"}}, "GET_PROMO_SCENARIO_IDS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.GET_PROMO_SCENARIO_IDS", "name": "GET_PROMO_SCENARIO_IDS", "setter_type": null, "type": "builtins.str"}}, "GET_PROMO_STACKED_OFFERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.GET_PROMO_STACKED_OFFERS", "name": "GET_PROMO_STACKED_OFFERS", "setter_type": null, "type": "builtins.str"}}, "GET_SCENARIO_DATA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.GET_SCENARIO_DATA", "name": "GET_SCENARIO_DATA", "setter_type": null, "type": "builtins.str"}}, "GET_SCENARIO_NAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.GET_SCENARIO_NAME", "name": "GET_SCENARIO_NAME", "setter_type": null, "type": "builtins.str"}}, "GET_SPECIAL_OFFERS_LIST_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.GET_SPECIAL_OFFERS_LIST_QUERY", "name": "GET_SPECIAL_OFFERS_LIST_QUERY", "setter_type": null, "type": "builtins.str"}}, "GET_SPECIAL_OFFER_DETAILS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.GET_SPECIAL_OFFER_DETAILS_QUERY", "name": "GET_SPECIAL_OFFER_DETAILS_QUERY", "setter_type": null, "type": "builtins.str"}}, "GET_STACKED_OFFERS_OF_PROMO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.GET_STACKED_OFFERS_OF_PROMO", "name": "GET_STACKED_OFFERS_OF_PROMO", "setter_type": null, "type": "builtins.str"}}, "GET_STORE_DETAILS_OF_PROMO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.GET_STORE_DETAILS_OF_PROMO", "name": "GET_STORE_DETAILS_OF_PROMO", "setter_type": null, "type": "builtins.str"}}, "GET_TIER_DATA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.GET_TIER_DATA", "name": "GET_TIER_DATA", "setter_type": null, "type": "builtins.str"}}, "GET_VENDOR_FUNDING_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.GET_VENDOR_FUNDING_TYPES", "name": "GET_VENDOR_FUNDING_TYPES", "setter_type": null, "type": "builtins.str"}}, "INSERT_ACTION_LOG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.INSERT_ACTION_LOG", "name": "INSERT_ACTION_LOG", "setter_type": null, "type": "builtins.str"}}, "INSERT_TB_REPORT_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.INSERT_TB_REPORT_QUERY", "name": "INSERT_TB_REPORT_QUERY", "setter_type": null, "type": "builtins.str"}}, "IS_FINALISED_OR_EXECUTION_APPROVED_PROMO_PRESENT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.IS_FINALISED_OR_EXECUTION_APPROVED_PROMO_PRESENT", "name": "IS_FINALISED_OR_EXECUTION_APPROVED_PROMO_PRESENT", "setter_type": null, "type": "builtins.str"}}, "IS_FINALIZED_SCENARIO_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.IS_FINALIZED_SCENARIO_QUERY", "name": "IS_FINALIZED_SCENARIO_QUERY", "setter_type": null, "type": "builtins.str"}}, "IS_PROMO_PRODUCT_DISCOUNT_SKU_LEVEL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.IS_PROMO_PRODUCT_DISCOUNT_SKU_LEVEL", "name": "IS_PROMO_PRODUCT_DISCOUNT_SKU_LEVEL", "setter_type": null, "type": "builtins.str"}}, "IS_PROMO_STORE_DISCOUNT_STORE_LEVEL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.IS_PROMO_STORE_DISCOUNT_STORE_LEVEL", "name": "IS_PROMO_STORE_DISCOUNT_STORE_LEVEL", "setter_type": null, "type": "builtins.str"}}, "MONTHLY_PERFORMANCE_GRAPH_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.MONTHLY_PERFORMANCE_GRAPH_QUERY", "name": "MONTHLY_PERFORMANCE_GRAPH_QUERY", "setter_type": null, "type": "builtins.str"}}, "MONTHLY_PERFORMANCE_GRAPH_QUERY_BY_IDS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.MONTHLY_PERFORMANCE_GRAPH_QUERY_BY_IDS", "name": "MONTHLY_PERFORMANCE_GRAPH_QUERY_BY_IDS", "setter_type": null, "type": "builtins.str"}}, "OVERALL_SIM_RESULTS_DETAILS_DOWNLOADS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.OVERALL_SIM_RESULTS_DETAILS_DOWNLOADS_QUERY", "name": "OVERALL_SIM_RESULTS_DETAILS_DOWNLOADS_QUERY", "setter_type": null, "type": "builtins.str"}}, "OVERRIDE_FORECAST_FOR_A_PROMO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.OVERRIDE_FORECAST_FOR_A_PROMO", "name": "OVERRIDE_FORECAST_FOR_A_PROMO", "setter_type": null, "type": "builtins.str"}}, "PROMOS_PROCESS_CHECK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.PROMOS_PROCESS_CHECK", "name": "PROMOS_PROCESS_CHECK", "setter_type": null, "type": "builtins.str"}}, "QUARTERLY_PERFORMANCE_GRAPH_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.QUARTERLY_PERFORMANCE_GRAPH_QUERY", "name": "QUARTERLY_PERFORMANCE_GRAPH_QUERY", "setter_type": null, "type": "builtins.str"}}, "QUARTERLY_PERFORMANCE_GRAPH_QUERY_BY_IDS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.QUARTERLY_PERFORMANCE_GRAPH_QUERY_BY_IDS", "name": "QUARTERLY_PERFORMANCE_GRAPH_QUERY_BY_IDS", "setter_type": null, "type": "builtins.str"}}, "REOPTIMISATION_PRE_PROCESS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.REOPTIMISATION_PRE_PROCESS", "name": "REOPTIMISATION_PRE_PROCESS", "setter_type": null, "type": "builtins.str"}}, "SAVE_EXMD_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.SAVE_EXMD_QUERY", "name": "SAVE_EXMD_QUERY", "setter_type": null, "type": "builtins.str"}}, "SET_DEFAULT_FOR_SCENARIO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.SET_DEFAULT_FOR_SCENARIO", "name": "SET_DEFAULT_FOR_SCENARIO", "setter_type": null, "type": "builtins.str"}}, "STEP1_SAVE_DETAILS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.STEP1_SAVE_DETAILS_QUERY", "name": "STEP1_SAVE_DETAILS_QUERY", "setter_type": null, "type": "builtins.str"}}, "STEP1_SAVE_PROMO_LIFECYCLE_INDICATOR_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.STEP1_SAVE_PROMO_LIFECYCLE_INDICATOR_QUERY", "name": "STEP1_SAVE_PROMO_LIFECYCLE_INDICATOR_QUERY", "setter_type": null, "type": "builtins.str"}}, "STEP1_SAVE_PROMO_PRODUCT_DETAILS_BASED_ON_PRODUCT_HIERARCHY_INPUTS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.STEP1_SAVE_PROMO_PRODUCT_DETAILS_BASED_ON_PRODUCT_HIERARCHY_INPUTS_QUERY", "name": "STEP1_SAVE_PROMO_PRODUCT_DETAILS_BASED_ON_PRODUCT_HIERARCHY_INPUTS_QUERY", "setter_type": null, "type": "builtins.str"}}, "STEP1_SAVE_PROMO_PRODUCT_DETAILS_BASED_ON_PRODUCT_IDS_INPUTS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "promotions.queries.STEP1_SAVE_PROMO_PRODUCT_DETAILS_BASED_ON_PRODUCT_IDS_INPUTS_QUERY", "name": "STEP1_SAVE_PROMO_PRODUCT_DETAILS_BASED_ON_PRODUCT_IDS_INPUTS_QUERY", "setter_type": null, "type": "builtins.str"}}, "STEP1_SAVE_PROMO_PRODUCT_DETAILS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.STEP1_SAVE_PROMO_PRODUCT_DETAILS_QUERY", "name": "STEP1_SAVE_PROMO_PRODUCT_DETAILS_QUERY", "setter_type": null, "type": "builtins.str"}}, "STEP1_SAVE_PROMO_PRODUCT_GROUP_BASED_ON_PG_INPUTS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.STEP1_SAVE_PROMO_PRODUCT_GROUP_BASED_ON_PG_INPUTS_QUERY", "name": "STEP1_SAVE_PROMO_PRODUCT_GROUP_BASED_ON_PG_INPUTS_QUERY", "setter_type": null, "type": "builtins.str"}}, "STEP1_SAVE_PROMO_PRODUCT_HIERARCHY_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.STEP1_SAVE_PROMO_PRODUCT_HIERARCHY_QUERY", "name": "STEP1_SAVE_PROMO_PRODUCT_HIERARCHY_QUERY", "setter_type": null, "type": "builtins.str"}}, "STEP1_SAVE_PROMO_SG_DETAILS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.STEP1_SAVE_PROMO_SG_DETAILS_QUERY", "name": "STEP1_SAVE_PROMO_SG_DETAILS_QUERY", "setter_type": null, "type": "builtins.str"}}, "STEP1_SAVE_PROMO_SG_HIERARCHY_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.STEP1_SAVE_PROMO_SG_HIERARCHY_QUERY", "name": "STEP1_SAVE_PROMO_SG_HIERARCHY_QUERY", "setter_type": null, "type": "builtins.str"}}, "STEP1_SAVE_PROMO_STORE_DETAILS_BASED_ON_BNM_STORE_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "promotions.queries.STEP1_SAVE_PROMO_STORE_DETAILS_BASED_ON_BNM_STORE_QUERY", "name": "STEP1_SAVE_PROMO_STORE_DETAILS_BASED_ON_BNM_STORE_QUERY", "setter_type": null, "type": "builtins.str"}}, "STEP1_SAVE_PROMO_STORE_DETAILS_BASED_ON_ECOM_STORE_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "promotions.queries.STEP1_SAVE_PROMO_STORE_DETAILS_BASED_ON_ECOM_STORE_QUERY", "name": "STEP1_SAVE_PROMO_STORE_DETAILS_BASED_ON_ECOM_STORE_QUERY", "setter_type": null, "type": "builtins.str"}}, "STEP1_SAVE_PROMO_STORE_DETAILS_BASED_ON_STORE_IDS_INPUTS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "promotions.queries.STEP1_SAVE_PROMO_STORE_DETAILS_BASED_ON_STORE_IDS_INPUTS_QUERY", "name": "STEP1_SAVE_PROMO_STORE_DETAILS_BASED_ON_STORE_IDS_INPUTS_QUERY", "setter_type": null, "type": "builtins.str"}}, "STEP1_SAVE_PROMO_STORE_DETAILS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.STEP1_SAVE_PROMO_STORE_DETAILS_QUERY", "name": "STEP1_SAVE_PROMO_STORE_DETAILS_QUERY", "setter_type": null, "type": "builtins.str"}}, "STEP1_SAVE_PROMO_STORE_GROUP_BASED_ON_SG_INPUTS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "promotions.queries.STEP1_SAVE_PROMO_STORE_GROUP_BASED_ON_SG_INPUTS_QUERY", "name": "STEP1_SAVE_PROMO_STORE_GROUP_BASED_ON_SG_INPUTS_QUERY", "setter_type": null, "type": "builtins.str"}}, "STEP1_SAVE_PROMO_STORE_HIERARCHY_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.STEP1_SAVE_PROMO_STORE_HIERARCHY_QUERY", "name": "STEP1_SAVE_PROMO_STORE_HIERARCHY_QUERY", "setter_type": null, "type": "builtins.str"}}, "STEP1_SAVE_WHOLE_CATECORY_PROMO_PRODUCT_HIERARCHY_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.STEP1_SAVE_WHOLE_CATECORY_PROMO_PRODUCT_HIERARCHY_QUERY", "name": "STEP1_SAVE_WHOLE_CATECORY_PROMO_PRODUCT_HIERARCHY_QUERY", "setter_type": null, "type": "builtins.str"}}, "STEP1_UPDATE_PROMO_DETAILS_AT_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.STEP1_UPDATE_PROMO_DETAILS_AT_QUERY", "name": "STEP1_UPDATE_PROMO_DETAILS_AT_QUERY", "setter_type": null, "type": "builtins.str"}}, "STEP3_SAVE_TAGETS_METRICS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.STEP3_SAVE_TAGETS_METRICS", "name": "STEP3_SAVE_TAGETS_METRICS", "setter_type": null, "type": "builtins.str"}}, "STEP3_UPDATE_PROMO_DETAILS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.STEP3_UPDATE_PROMO_DETAILS_QUERY", "name": "STEP3_UPDATE_PROMO_DETAILS_QUERY", "setter_type": null, "type": "builtins.str"}}, "UPDATE_ACTION_LOG_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.UPDATE_ACTION_LOG_QUERY", "name": "UPDATE_ACTION_LOG_QUERY", "setter_type": null, "type": "builtins.str"}}, "UPDATE_DISCOUNT_RULES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.UPDATE_DISCOUNT_RULES", "name": "UPDATE_DISCOUNT_RULES", "setter_type": null, "type": "builtins.str"}}, "UPDATE_DISCOUNT_VALUES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.UPDATE_DISCOUNT_VALUES", "name": "UPDATE_DISCOUNT_VALUES", "setter_type": null, "type": "builtins.str"}}, "UPDATE_IS_AUTO_RESIMULATED_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.UPDATE_IS_AUTO_RESIMULATED_QUERY", "name": "UPDATE_IS_AUTO_RESIMULATED_QUERY", "setter_type": null, "type": "builtins.str"}}, "UPDATE_IS_PROCESSING_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.UPDATE_IS_PROCESSING_QUERY", "name": "UPDATE_IS_PROCESSING_QUERY", "setter_type": null, "type": "builtins.str"}}, "UPDATE_PLACEHOLDER_PROMO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.UPDATE_PLACEHOLDER_PROMO", "name": "UPDATE_PLACEHOLDER_PROMO", "setter_type": null, "type": "builtins.str"}}, "UPDATE_PROMO_PRODUCT_STORE_COUNT_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.UPDATE_PROMO_PRODUCT_STORE_COUNT_QUERY", "name": "UPDATE_PROMO_PRODUCT_STORE_COUNT_QUERY", "setter_type": null, "type": "builtins.str"}}, "UPDATE_PROMO_STATUS_AND_REFRESH_STACKED_PROMOS_MAPPING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.UPDATE_PROMO_STATUS_AND_REFRESH_STACKED_PROMOS_MAPPING", "name": "UPDATE_PROMO_STATUS_AND_REFRESH_STACKED_PROMOS_MAPPING", "setter_type": null, "type": "builtins.str"}}, "UPDATE_SCENARIO_NAME_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.UPDATE_SCENARIO_NAME_QUERY", "name": "UPDATE_SCENARIO_NAME_QUERY", "setter_type": null, "type": "builtins.str"}}, "UPDATE_STRATEGY_SIMULATION_FLAG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.UPDATE_STRATEGY_SIMULATION_FLAG", "name": "UPDATE_STRATEGY_SIMULATION_FLAG", "setter_type": null, "type": "builtins.str"}}, "VALIDATE_COPY_OFFER_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.VALIDATE_COPY_OFFER_QUERY", "name": "VALIDATE_COPY_OFFER_QUERY", "setter_type": null, "type": "builtins.str"}}, "VALIDATE_DELETE_TIER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.VALIDATE_DELETE_TIER", "name": "VALIDATE_DELETE_TIER", "setter_type": null, "type": "builtins.str"}}, "VALIDATE_MISSING_DISCOUNT_VALUES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.VALIDATE_MISSING_DISCOUNT_VALUES", "name": "VALIDATE_MISSING_DISCOUNT_VALUES", "setter_type": null, "type": "builtins.str"}}, "WEEKLY_PERFORMANCE_GRAPH_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.WEEKLY_PERFORMANCE_GRAPH_QUERY", "name": "WEEKLY_PERFORMANCE_GRAPH_QUERY", "setter_type": null, "type": "builtins.str"}}, "WEEKLY_PERFORMANCE_GRAPH_QUERY_BY_IDS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.WEEKLY_PERFORMANCE_GRAPH_QUERY_BY_IDS", "name": "WEEKLY_PERFORMANCE_GRAPH_QUERY_BY_IDS", "setter_type": null, "type": "builtins.str"}}, "WITHDRAW_PROMO_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries.WITHDRAW_PROMO_QUERY", "name": "WITHDRAW_PROMO_QUERY", "setter_type": null, "type": "builtins.str"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "promotions.queries.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "promotions.queries.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "promotions.queries.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "promotions.queries.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "promotions.queries.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "promotions.queries.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/Users/<USER>/impact/pricesmart-promo-v3/backend/promotions/queries.py"}