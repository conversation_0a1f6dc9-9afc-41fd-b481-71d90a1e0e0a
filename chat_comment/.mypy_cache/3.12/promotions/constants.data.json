{".class": "MypyFile", "_fullname": "promotions.constants", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ACTION_APPROVE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.ACTION_APPROVE", "name": "ACTION_APPROVE", "setter_type": null, "type": "builtins.str"}}, "ACTION_APPROVE_SCENARIO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.ACTION_APPROVE_SCENARIO", "name": "ACTION_APPROVE_SCENARIO", "setter_type": null, "type": "builtins.str"}}, "ACTION_ARCHIVE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.ACTION_ARCHIVE", "name": "ACTION_ARCHIVE", "setter_type": null, "type": "builtins.str"}}, "ACTION_COPY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.ACTION_COPY", "name": "ACTION_COPY", "setter_type": null, "type": "builtins.str"}}, "ACTION_CREATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.ACTION_CREATE", "name": "ACTION_CREATE", "setter_type": null, "type": "builtins.str"}}, "ACTION_DELETE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.ACTION_DELETE", "name": "ACTION_DELETE", "setter_type": null, "type": "builtins.str"}}, "ACTION_EDIT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.ACTION_EDIT", "name": "ACTION_EDIT", "setter_type": null, "type": "builtins.str"}}, "ACTION_EVENT_EDIT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.ACTION_EVENT_EDIT", "name": "ACTION_EVENT_EDIT", "setter_type": null, "type": "builtins.str"}}, "ACTION_EVENT_EDIT_OFFER_REFRESH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.ACTION_EVENT_EDIT_OFFER_REFRESH", "name": "ACTION_EVENT_EDIT_OFFER_REFRESH", "setter_type": null, "type": "builtins.str"}}, "ACTION_EVENT_RESIMULATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.ACTION_EVENT_RESIMULATE", "name": "ACTION_EVENT_RESIMULATE", "setter_type": null, "type": "builtins.str"}}, "ACTION_EVENT_RESIMULATE_OFFER_REFRESH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.ACTION_EVENT_RESIMULATE_OFFER_REFRESH", "name": "ACTION_EVENT_RESIMULATE_OFFER_REFRESH", "setter_type": null, "type": "builtins.str"}}, "ACTION_EXECUTE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.ACTION_EXECUTE", "name": "ACTION_EXECUTE", "setter_type": null, "type": "builtins.str"}}, "ACTION_EXECUTION_APPROVED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.ACTION_EXECUTION_APPROVED", "name": "ACTION_EXECUTION_APPROVED", "setter_type": null, "type": "builtins.str"}}, "ACTION_FINALIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.ACTION_FINALIZE", "name": "ACTION_FINALIZE", "setter_type": null, "type": "builtins.str"}}, "ACTION_GET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.ACTION_GET", "name": "ACTION_GET", "setter_type": null, "type": "builtins.str"}}, "ACTION_OPTIMISE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.ACTION_OPTIMISE", "name": "ACTION_OPTIMISE", "setter_type": null, "type": "builtins.str"}}, "ACTION_OVERRIDE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.ACTION_OVERRIDE", "name": "ACTION_OVERRIDE", "setter_type": null, "type": "builtins.str"}}, "ACTION_REFRESH_METRICS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.ACTION_REFRESH_METRICS", "name": "ACTION_REFRESH_METRICS", "setter_type": null, "type": "builtins.str"}}, "ACTION_SIMULATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.ACTION_SIMULATE", "name": "ACTION_SIMULATE", "setter_type": null, "type": "builtins.str"}}, "ACTION_VALUES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "promotions.constants.ACTION_VALUES", "name": "ACTION_VALUES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "ACTION_WITHDRAW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.ACTION_WITHDRAW", "name": "ACTION_WITHDRAW", "setter_type": null, "type": "builtins.str"}}, "AND": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.AND", "name": "AND", "setter_type": null, "type": "builtins.str"}}, "APPLICATION_VARIABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.APPLICATION_VARIABLE", "name": "APPLICATION_VARIABLE", "setter_type": null, "type": "builtins.str"}}, "APPROVED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.APPROVED", "name": "APPROVED", "setter_type": null, "type": "builtins.str"}}, "ARCHIVED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.ARCHIVED", "name": "ARCHIVED", "setter_type": null, "type": "builtins.str"}}, "BRAND_CID_COLUMN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.BRAND_CID_COLUMN", "name": "BRAND_CID_COLUMN", "setter_type": null, "type": "builtins.str"}}, "BRAND_CUQ_COLUMN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.BRAND_CUQ_COLUMN", "name": "BRAND_CUQ_COLUMN", "setter_type": null, "type": "builtins.str"}}, "BRAND_ID_KEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.BRAND_ID_KEY", "name": "BRAND_ID_KEY", "setter_type": null, "type": "builtins.str"}}, "BULK_RESIMULATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.BULK_RESIMULATE", "name": "BULK_RESIMULATE", "setter_type": null, "type": "builtins.str"}}, "CALENDAR_VIEW_REPORT_EXTENSIVE_DATA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.CALENDAR_VIEW_REPORT_EXTENSIVE_DATA", "name": "CALENDAR_VIEW_REPORT_EXTENSIVE_DATA", "setter_type": null, "type": "builtins.str"}}, "COMPLETED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.COMPLETED", "name": "COMPLETED", "setter_type": null, "type": "builtins.str"}}, "DATE_RANGE_CONDITION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.DATE_RANGE_CONDITION", "name": "DATE_RANGE_CONDITION", "setter_type": null, "type": "builtins.str"}}, "DATE_RANGE_CONDITION_INCLUSIVE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.DATE_RANGE_CONDITION_INCLUSIVE", "name": "DATE_RANGE_CONDITION_INCLUSIVE", "setter_type": null, "type": "builtins.str"}}, "DECISION_DASHBOARD_REPORT_EXTENSIVE_DATA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.DECISION_DASHBOARD_REPORT_EXTENSIVE_DATA", "name": "DECISION_DASHBOARD_REPORT_EXTENSIVE_DATA", "setter_type": null, "type": "builtins.str"}}, "DELETED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.DELETED", "name": "DELETED", "setter_type": null, "type": "builtins.str"}}, "DRAFT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.DRAFT", "name": "DRAFT", "setter_type": null, "type": "builtins.str"}}, "DRAFT_COPIED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.DRAFT_COPIED", "name": "DRAFT_COPIED", "setter_type": null, "type": "builtins.str"}}, "EXCLUSION_CLASS_ID_COLUMN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.EXCLUSION_CLASS_ID_COLUMN", "name": "EXCLUSION_CLASS_ID_COLUMN", "setter_type": null, "type": "builtins.str"}}, "EXCLUSION_DEPT_ID_COLUMN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.EXCLUSION_DEPT_ID_COLUMN", "name": "EXCLUSION_DEPT_ID_COLUMN", "setter_type": null, "type": "builtins.str"}}, "EXCLUSION_DIV_ID_COLUMN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.EXCLUSION_DIV_ID_COLUMN", "name": "EXCLUSION_DIV_ID_COLUMN", "setter_type": null, "type": "builtins.str"}}, "EXCLUSION_GROUP_ID_COLUMN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.EXCLUSION_GROUP_ID_COLUMN", "name": "EXCLUSION_GROUP_ID_COLUMN", "setter_type": null, "type": "builtins.str"}}, "EXCLUSION_LIFECYCLE_COLUMN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.EXCLUSION_LIFECYCLE_COLUMN", "name": "EXCLUSION_LIFECYCLE_COLUMN", "setter_type": null, "type": "builtins.str"}}, "EXCLUSION_MFG_ID_COLUMN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.EXCLUSION_MFG_ID_COLUMN", "name": "EXCLUSION_MFG_ID_COLUMN", "setter_type": null, "type": "builtins.str"}}, "EXCLUSION_SKU_ID_COLUMN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.EXCLUSION_SKU_ID_COLUMN", "name": "EXCLUSION_SKU_ID_COLUMN", "setter_type": null, "type": "builtins.str"}}, "EXCLUSION_SUBCLASS_ID_COLUMN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.EXCLUSION_SUBCLASS_ID_COLUMN", "name": "EXCLUSION_SUBCLASS_ID_COLUMN", "setter_type": null, "type": "builtins.str"}}, "EXCLUSION_UPLOAD_FILE_COLUMNS_LIST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "promotions.constants.EXCLUSION_UPLOAD_FILE_COLUMNS_LIST", "name": "EXCLUSION_UPLOAD_FILE_COLUMNS_LIST", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "EXECUTION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.EXECUTION", "name": "EXECUTION", "setter_type": null, "type": "builtins.str"}}, "EXECUTION_APPROVAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.EXECUTION_APPROVAL", "name": "EXECUTION_APPROVAL", "setter_type": null, "type": "builtins.str"}}, "FINALIZED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.FINALIZED", "name": "FINALIZED", "setter_type": null, "type": "builtins.str"}}, "L0_CID_COLUMN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.L0_CID_COLUMN", "name": "L0_CID_COLUMN", "setter_type": null, "type": "builtins.str"}}, "L0_CUQ_COLUMN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.L0_CUQ_COLUMN", "name": "L0_CUQ_COLUMN", "setter_type": null, "type": "builtins.str"}}, "L0_ID_KEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.L0_ID_KEY", "name": "L0_ID_KEY", "setter_type": null, "type": "builtins.str"}}, "L1_CID_COLUMN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.L1_CID_COLUMN", "name": "L1_CID_COLUMN", "setter_type": null, "type": "builtins.str"}}, "L1_CUQ_COLUMN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.L1_CUQ_COLUMN", "name": "L1_CUQ_COLUMN", "setter_type": null, "type": "builtins.str"}}, "L1_ID_KEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.L1_ID_KEY", "name": "L1_ID_KEY", "setter_type": null, "type": "builtins.str"}}, "L2_CID_COLUMN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.L2_CID_COLUMN", "name": "L2_CID_COLUMN", "setter_type": null, "type": "builtins.str"}}, "L2_CUQ_COLUMN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.L2_CUQ_COLUMN", "name": "L2_CUQ_COLUMN", "setter_type": null, "type": "builtins.str"}}, "L2_ID_KEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.L2_ID_KEY", "name": "L2_ID_KEY", "setter_type": null, "type": "builtins.str"}}, "L3_CID_COLUMN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.L3_CID_COLUMN", "name": "L3_CID_COLUMN", "setter_type": null, "type": "builtins.str"}}, "L3_CUQ_COLUMN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.L3_CUQ_COLUMN", "name": "L3_CUQ_COLUMN", "setter_type": null, "type": "builtins.str"}}, "L3_ID_KEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.L3_ID_KEY", "name": "L3_ID_KEY", "setter_type": null, "type": "builtins.str"}}, "L4_CID_COLUMN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.L4_CID_COLUMN", "name": "L4_CID_COLUMN", "setter_type": null, "type": "builtins.str"}}, "L4_CUQ_COLUMN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.L4_CUQ_COLUMN", "name": "L4_CUQ_COLUMN", "setter_type": null, "type": "builtins.str"}}, "L4_ID_KEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.L4_ID_KEY", "name": "L4_ID_KEY", "setter_type": null, "type": "builtins.str"}}, "L5_CID_COLUMN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.L5_CID_COLUMN", "name": "L5_CID_COLUMN", "setter_type": null, "type": "builtins.str"}}, "L5_CUQ_COLUMN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.L5_CUQ_COLUMN", "name": "L5_CUQ_COLUMN", "setter_type": null, "type": "builtins.str"}}, "L5_ID_KEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.L5_ID_KEY", "name": "L5_ID_KEY", "setter_type": null, "type": "builtins.str"}}, "LIFECYCLE_INDICATOR_KEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.LIFECYCLE_INDICATOR_KEY", "name": "LIFECYCLE_INDICATOR_KEY", "setter_type": null, "type": "builtins.str"}}, "MONTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.MONTH", "name": "MONTH", "setter_type": null, "type": "builtins.str"}}, "NO_RESPONSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.NO_RESPONSE", "name": "NO_RESPONSE", "setter_type": null, "type": "builtins.str"}}, "OFFER_STATUS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "promotions.constants.OFFER_STATUS", "name": "OFFER_STATUS", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "OPTIMISE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.OPTIMISE", "name": "OPTIMISE", "setter_type": null, "type": "builtins.str"}}, "OVERALL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.OVERALL", "name": "OVERALL", "setter_type": null, "type": "builtins.int"}}, "PERFORMANCE_COMPARISON_VALUE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.PERFORMANCE_COMPARISON_VALUE", "name": "PERFORMANCE_COMPARISON_VALUE", "setter_type": null, "type": "builtins.int"}}, "PLACEHOLDER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.PLACEHOLDER", "name": "PLACEHOLDER", "setter_type": null, "type": "builtins.str"}}, "PRODUCT_HIERARCHY_CID_MAPPING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "promotions.constants.PRODUCT_HIERARCHY_CID_MAPPING", "name": "PRODUCT_HIERARCHY_CID_MAPPING", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "PROMO_SIMULATOR_REPORT_EXTENSIVE_DATA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.PROMO_SIMULATOR_REPORT_EXTENSIVE_DATA", "name": "PROMO_SIMULATOR_REPORT_EXTENSIVE_DATA", "setter_type": null, "type": "builtins.str"}}, "PROMO_SKU_PRICE_DATA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.PROMO_SKU_PRICE_DATA", "name": "PROMO_SKU_PRICE_DATA", "setter_type": null, "type": "builtins.str"}}, "PROMO_STATUS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "promotions.constants.PROMO_STATUS", "name": "PROMO_STATUS", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "QUARTER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.QUARTER", "name": "QUARTER", "setter_type": null, "type": "builtins.str"}}, "RECOMMENDATION_TYPE_IDS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "promotions.constants.RECOMMENDATION_TYPE_IDS", "name": "RECOMMENDATION_TYPE_IDS", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "REJECTED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.REJECTED", "name": "REJECTED", "setter_type": null, "type": "builtins.str"}}, "RESIMULATION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "promotions.constants.RESIMULATION", "name": "RESIMULATION", "setter_type": null, "type": "builtins.str"}}, "SCREEN_CALENDAR_VIEW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.SCREEN_CALENDAR_VIEW", "name": "SCREEN_CALENDAR_VIEW", "setter_type": null, "type": "builtins.str"}}, "SCREEN_DECISION_DASHBOARD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.SCREEN_DECISION_DASHBOARD", "name": "SCREEN_DECISION_DASHBOARD", "setter_type": null, "type": "builtins.str"}}, "SCREEN_MARKETING_CALENDAR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.SCREEN_MARKETING_CALENDAR", "name": "SCREEN_MARKETING_CALENDAR", "setter_type": null, "type": "builtins.str"}}, "SCREEN_STEP0": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.SCREEN_STEP0", "name": "SCREEN_STEP0", "setter_type": null, "type": "builtins.str"}}, "SCREEN_STEP1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.SCREEN_STEP1", "name": "SCREEN_STEP1", "setter_type": null, "type": "builtins.str"}}, "SCREEN_STEP2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.SCREEN_STEP2", "name": "SCREEN_STEP2", "setter_type": null, "type": "builtins.str"}}, "SCREEN_STEP3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.SCREEN_STEP3", "name": "SCREEN_STEP3", "setter_type": null, "type": "builtins.str"}}, "SCREEN_TIER_MANAGEMENT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.SCREEN_TIER_MANAGEMENT", "name": "SCREEN_TIER_MANAGEMENT", "setter_type": null, "type": "builtins.str"}}, "SCREEN_VALUES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "promotions.constants.SCREEN_VALUES", "name": "SCREEN_VALUES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "SCREEN_WORKBENCH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.SCREEN_WORKBENCH", "name": "SCREEN_WORKBENCH", "setter_type": null, "type": "builtins.str"}}, "SENT_FOR_APPROVAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.SENT_FOR_APPROVAL", "name": "SENT_FOR_APPROVAL", "setter_type": null, "type": "builtins.str"}}, "SIMULATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.SIMULATE", "name": "SIMULATE", "setter_type": null, "type": "builtins.str"}}, "SIMULATOR_ACTIONS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "promotions.constants.SIMULATOR_ACTIONS", "name": "SIMULATOR_ACTIONS", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "SIM_ENABLED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.SIM_ENABLED", "name": "SIM_ENABLED", "setter_type": null, "type": "builtins.str"}}, "SPECIAL_CHARACTER_MAPPING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "promotions.constants.SPECIAL_CHARACTER_MAPPING", "name": "SPECIAL_CHARACTER_MAPPING", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "TO_FINALIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.TO_FINALIZE", "name": "TO_FINALIZE", "setter_type": null, "type": "builtins.str"}}, "TYPE_COPY_PASTE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.TYPE_COPY_PASTE", "name": "TYPE_COPY_PASTE", "setter_type": null, "type": "builtins.int"}}, "TYPE_PRODUCT_GROUP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.TYPE_PRODUCT_GROUP", "name": "TYPE_PRODUCT_GROUP", "setter_type": null, "type": "builtins.int"}}, "TYPE_PRODUCT_HIERARCHY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.TYPE_PRODUCT_HIERARCHY", "name": "TYPE_PRODUCT_HIERARCHY", "setter_type": null, "type": "builtins.int"}}, "TYPE_UPLOAD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.TYPE_UPLOAD", "name": "TYPE_UPLOAD", "setter_type": null, "type": "builtins.int"}}, "USER_SCENARIO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "promotions.constants.USER_SCENARIO", "name": "USER_SCENARIO", "setter_type": null, "type": "builtins.str"}}, "VIEW_BY_OPTIONS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "promotions.constants.VIEW_BY_OPTIONS", "name": "VIEW_BY_OPTIONS", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "WEEK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.WEEK", "name": "WEEK", "setter_type": null, "type": "builtins.str"}}, "WHERE_CONDITION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.WHERE_CONDITION", "name": "WHERE_CONDITION", "setter_type": null, "type": "builtins.str"}}, "WORKBENCH_REPORT_EXTENSIVE_DATA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.constants.WORKBENCH_REPORT_EXTENSIVE_DATA", "name": "WORKBENCH_REPORT_EXTENSIVE_DATA", "setter_type": null, "type": "builtins.str"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "promotions.constants.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "promotions.constants.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "promotions.constants.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "promotions.constants.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "promotions.constants.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "promotions.constants.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/Users/<USER>/impact/pricesmart-promo-v3/backend/promotions/constants.py"}