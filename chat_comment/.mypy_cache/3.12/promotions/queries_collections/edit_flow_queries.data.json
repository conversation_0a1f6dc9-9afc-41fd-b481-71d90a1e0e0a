{".class": "MypyFile", "_fullname": "promotions.queries_collections.edit_flow_queries", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "STEP0_EDIT_PROMO_DETAILS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries_collections.edit_flow_queries.STEP0_EDIT_PROMO_DETAILS_QUERY", "name": "STEP0_EDIT_PROMO_DETAILS_QUERY", "setter_type": null, "type": "builtins.str"}}, "STEP1_UPDATE_PROMO_DETAILS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries_collections.edit_flow_queries.STEP1_UPDATE_PROMO_DETAILS_QUERY", "name": "STEP1_UPDATE_PROMO_DETAILS_QUERY", "setter_type": null, "type": "builtins.str"}}, "UPDATE_PROMO_DEATILS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries_collections.edit_flow_queries.UPDATE_PROMO_DEATILS_QUERY", "name": "UPDATE_PROMO_DEATILS_QUERY", "setter_type": null, "type": "builtins.str"}}, "UPDATE_PROMO_DETAILS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries_collections.edit_flow_queries.UPDATE_PROMO_DETAILS_QUERY", "name": "UPDATE_PROMO_DETAILS_QUERY", "setter_type": null, "type": "builtins.str"}}, "UPDATE_PROMO_METRICS_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "promotions.queries_collections.edit_flow_queries.UPDATE_PROMO_METRICS_QUERY", "name": "UPDATE_PROMO_METRICS_QUERY", "setter_type": null, "type": "builtins.str"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "promotions.queries_collections.edit_flow_queries.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "promotions.queries_collections.edit_flow_queries.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "promotions.queries_collections.edit_flow_queries.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "promotions.queries_collections.edit_flow_queries.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "promotions.queries_collections.edit_flow_queries.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "promotions.queries_collections.edit_flow_queries.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/Users/<USER>/impact/pricesmart-promo-v3/backend/promotions/queries_collections/edit_flow_queries.py"}