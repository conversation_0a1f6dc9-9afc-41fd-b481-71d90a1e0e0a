{"connection_string": "dbname=balsam_dev user=mtp-dev password=feVSTbtfit70 host=********* port=5432", "mkd_connection_string": "dbname=balsam_dev user=mtp-dev password=feVSTbtfit70 host=********* port=5432", "google_api_key": "AIzaSyBA-kyB6yLQoBeK26Vmn8QR0k2_5RaroBU", "mojo_access_key": "4b32ecaffda5cb328659a02ae51735dfff3ef3c7", "mojo_ticket_queue_id": 174815, "db_location": "cloud-sql-dev", "db_host": "*********", "db_port": "5432", "db_user": "mtp-dev", "db_password": "feVSTbtfit70", "db_name": "balsam_dev", "mkd_db_name": "balsam_dev", "x509_cert": "-----<PERSON><PERSON><PERSON> CERTIFICATE-----MIIC<PERSON>DCCAdigAwIBAgIQJl9iRCuhJIxFLHz7BIQT+DANBgkqhkiG9w0BAQsFADA0MTIwMAYDVQQDEylNaWNyb3NvZnQgQXp1cmUgRmVkZXJhdGVkIFNTTyBDZXJ0aWZpY2F0ZTAeFw0yMzAzMDExODM5NDVaFw0yNjAzMDExODM5NDVaMDQxMjAwBgNVBAMTKU1pY3Jvc29mdCBBenVyZSBGZWRlcmF0ZWQgU1NPIENlcnRpZmljYXRlMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA0fLRcc9xVkd6p6STd4W14TLZE92hGDOHb5hroXL6Um/7yvO4hE7TqyJd70DlwaGrnMbmdhkqjLv7YJ/d7XdsXRLPn0+kM/K4Id61u7ApIK0YPd3vLHAwD+kNl8KB6hZCS/c6Bf68aw60KXHnUtyKGdzdaPPf7LOmk2YNZXoTIwiwb/0lxljlbmUNPEgrAeqHqUMAbgZ2/Brg5bnOVInSSxlBfvCqonK4G+hHjYIddBnd6vVYqT8LF8WwbznWZiRDgNIIFYEj2RSIS86MYeOUqrLJc/GxW76Hgdn36MG7ClsIueSpS0OWdbXB72YbYglO6sy/Zid/cXNz4lwExZK9gQIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQCx51de0O+vWGB8Iwqwscm4JbbMAX9+vwk62Vnkli83hL266UsaT3op68N9y4PzxIRe+rC22BjBwx4Mpu9L98uWafSfI0jdG4m/TPDPTO+7sodwp0HghKb4VTvtqErnKXM0OWa3za9Oujd4RJGfUTafYKExFRvQirBii73o260NfzdtBKGVKiCd2hgwGQdKKvpE5O6wY5SKETHMQ3UrfyVuxCmHkbvMyFDyqqxdF06w3xFD8oLRC+B5Uz3akIP7NW+WCwpqznmNRl4B9Yq2rV0g+HIRLgqL6S4BrqgGmroplScUVsy+afTX2IhXaOG2h4y0piOVT3wFzs1KysGKb91V-----END CERTIFICATE-----", "sftp_connection_details": {"host": "impactanalytics.files.com", "port": 22, "username": "big_lots_int", "password": "CpJZ@flr4mJG"}, "be_key": "BUrk4amWs9aqujqk92GCO061114T2CbV", "snowflake_user": "IADATAINGESTION", "snowflake_password": "k8m6Jkda@aDMPVX", "snowflake_account": "tmb92638.us-east-1", "snowflake_warehouse": "IMPACT1", "snowflake_database": "so5_integration_test", "snowflake_schema": "so5_integration", "optimization_user": "<EMAIL>", "optimization_user_pass": "Wp6gh&qw2*", "aws_access_key_id": "********************", "aws_secret_access_key": "dD2DomcfgGEakkrrj3dkSdH2cqkCwaY4uD6xSefr", "OPENAI_API_KEY": "********************************************************************************************************************************************************************"}