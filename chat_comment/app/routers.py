from fastapi import APIRouter
from notifications.controller import router as notification_router
from server_sent_events.controller import sse_router
from pricesmart_common import constants as global_constants
from pricesmart_common.controller import common_router
from chat.controller import router as chat_router
from comment.controller import router as comment_router

router = APIRouter()

# Core infrastructure routers
router.include_router(router=sse_router, prefix=global_constants.API_PREFIX)
router.include_router(router=notification_router, prefix=global_constants.API_PREFIX)
router.include_router(router=common_router, prefix=global_constants.API_V3_PREFIX)

# Chat and Comment functionality
router.include_router(router=chat_router, prefix=global_constants.API_V3_PREFIX)
router.include_router(router=comment_router, prefix=global_constants.API_V3_PREFIX)

