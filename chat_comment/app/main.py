import json
import os
from dotenv import load_dotenv
load_dotenv()
from contextlib import asynccontextmanager
import firebase_admin
import pricesmart_common.constants as global_constants
from common import constants as com_const
from common.google_secret_manager_client import get_secret_value
from configuration.environment import environment
from fastapi import Depends, FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.utils import get_openapi
from google.cloud import secretmanager
from middleware.validation import AppversionMiddleware
from middleware.custom_exception_middleware import CustomExceptionMiddleware
from pricesmart_common import data as common_data
from pricesmart_common.models import BaseResponseBody

from .dependencies import check_maintenance, get_event_dependency
from pricesmart_common import constants as pricesmart_common_constants
from client_configuration import utils as client_configuration_utils
from app.routers import router
from exceptions import exception_handlers

def load_env_from_secrets_manager():
    """Loads env from google secret manager"""
    # initialize synchronous secret manager client
    try:
        with open(f"/mtp/secret") as file_obj:
            env_conf = json.load(file_obj)
    except FileNotFoundError:
        # fetch using API.
        secrets_client = secretmanager.SecretManagerServiceClient()
        env_conf = get_secret_value(
            secrets_client,
            environment.secret_project,
            f"{com_const.SECRET_KEY_ENV_VARIABLES}_{environment.ENVIRONMENT}",
        )
        env_conf = json.loads(env_conf)

    except ValueError as ex:
        print(
            "Main: load_env_from_secrets_manager: Error while fetching config : %s",
            str(ex),
        )
    finally:
        os.environ.update(env_conf)


@asynccontextmanager
async def lifespan(_: FastAPI):
    if environment.environment != pricesmart_common_constants.LOCAL_ENV:
        load_env_from_secrets_manager()
        firebase_admin.initialize_app()
    await get_event_dependency()
    identifiers = await common_data.get_client_configuration(True)
    client_configuration_utils.update_client_configuration_constants(identifiers)
    yield


app = FastAPI(
    title=global_constants.API_TITLE,
    docs_url=f"{global_constants.API_PREFIX}/docs" if environment.ENVIRONMENT in [com_const.DEPLOY_ENV_DEV, com_const.DEPLOY_ENV_TEST, com_const.DEPLOY_ENV_LOCAL] else None,
    redoc_url=f"{global_constants.API_PREFIX}/redocs" if environment.ENVIRONMENT in [com_const.DEPLOY_ENV_DEV, com_const.DEPLOY_ENV_TEST, com_const.DEPLOY_ENV_LOCAL] else None,
    openapi_url=f"{global_constants.API_PREFIX}/openapi.json" if environment.ENVIRONMENT in [com_const.DEPLOY_ENV_DEV, com_const.DEPLOY_ENV_TEST, com_const.DEPLOY_ENV_LOCAL] else None,
    dependencies=[Depends(check_maintenance)],
    lifespan=lifespan,
    routes=router.routes
)


app.add_exception_handler(Exception,exception_handlers.custom_exception_handler)
app.add_exception_handler(HTTPException,exception_handlers.custom_exception_handler)

app.add_middleware(AppversionMiddleware)
app.add_middleware(CustomExceptionMiddleware)

if environment.environment != pricesmart_common_constants.LOCAL_ENV:
    excluded_apis = [
        "/sse-output",
        "/docs",
        "/redocs",
        "/openapi.json",
        "/user/notify",
    ]
    from tenant.middleware import TenantContextMiddleware
    app.add_middleware(TenantContextMiddleware, excluded_api=excluded_apis)
else:
    from middleware.local_tenant_context import LocalTenantContextMiddleware
    app.add_middleware(LocalTenantContextMiddleware)

input_file = open("./CORS.json")
origins = json.load(input_file)
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["content-disposition"],
)

@app.get(f"{global_constants.API_PREFIX}/healthz", include_in_schema=False)
async def root():
    return BaseResponseBody(
        message="Server up and running! on database :" + environment.db_location
    )

def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema
    openapi_schema = get_openapi(
        title=app.title, version=app.version, routes=app.routes
    )

    # looks for the error 422 and removes it
    def remove_422(openapi_dict):
        for k, v in openapi_dict.items():
            if isinstance(v, dict) and "422" in v.keys():
                del v["422"]
                break
            elif isinstance(v, dict):
                remove_422(v)
        return openapi_dict

    openapi_schema = remove_422(openapi_schema)

    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi
